<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kripto Balina Takip Sistemi</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-whale"></i>
                <h1>Kripto Balina Takip</h1>
            </div>
            <nav class="nav">
                <a href="#dashboard" class="nav-link active">Dashboard</a>
                <a href="#alerts" class="nav-link">Uyarılar</a>
                <a href="#analytics" class="nav-link">Analitik</a>
            </nav>
        </div>
    </header>

    <main class="main">
        <div class="container">
            <!-- İstatistik Kartları -->
            <section class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-whale"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Toplam Balina İşlemi</h3>
                            <p class="stat-number" id="totalWhales">0</p>
                            <span class="stat-change positive">+12%</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Toplam Hacim</h3>
                            <p class="stat-number" id="totalVolume">$0</p>
                            <span class="stat-change positive">+8.5%</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3>En Büyük İşlem</h3>
                            <p class="stat-number" id="largestTx">$0</p>
                            <span class="stat-change negative">-2.1%</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Son 24 Saat</h3>
                            <p class="stat-number" id="last24h">0</p>
                            <span class="stat-change positive">+15.3%</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Filtreler -->
            <section class="filters-section">
                <div class="filters">
                    <div class="filter-group">
                        <label for="coinFilter">Kripto Para:</label>
                        <select id="coinFilter">
                            <option value="all">Tümü</option>
                            <optgroup label="Top Kripto Paralar">
                                <option value="BTC">Bitcoin (BTC)</option>
                                <option value="ETH">Ethereum (ETH)</option>
                                <option value="USDT">Tether (USDT)</option>
                                <option value="BNB">BNB (BNB)</option>
                                <option value="SOL">Solana (SOL)</option>
                                <option value="USDC">USD Coin (USDC)</option>
                                <option value="XRP">XRP (XRP)</option>
                                <option value="DOGE">Dogecoin (DOGE)</option>
                                <option value="ADA">Cardano (ADA)</option>
                                <option value="AVAX">Avalanche (AVAX)</option>
                            </optgroup>
                            <optgroup label="DeFi & Altcoinler">
                                <option value="LINK">Chainlink (LINK)</option>
                                <option value="MATIC">Polygon (MATIC)</option>
                                <option value="UNI">Uniswap (UNI)</option>
                                <option value="LTC">Litecoin (LTC)</option>
                                <option value="DOT">Polkadot (DOT)</option>
                                <option value="SHIB">Shiba Inu (SHIB)</option>
                                <option value="WBTC">Wrapped Bitcoin (WBTC)</option>
                                <option value="DAI">Dai (DAI)</option>
                                <option value="ATOM">Cosmos (ATOM)</option>
                                <option value="ETC">Ethereum Classic (ETC)</option>
                            </optgroup>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="minAmount">Min. Tutar ($):</label>
                        <input type="number" id="minAmount" placeholder="1000000" min="100000">
                    </div>
                    <div class="filter-group">
                        <label for="timeFilter">Zaman:</label>
                        <select id="timeFilter">
                            <option value="1h">Son 1 Saat</option>
                            <option value="24h" selected>Son 24 Saat</option>
                            <option value="7d">Son 7 Gün</option>
                            <option value="30d">Son 30 Gün</option>
                        </select>
                    </div>
                    <button class="filter-btn" onclick="applyFilters()">
                        <i class="fas fa-filter"></i> Filtrele
                    </button>
                </div>
            </section>

            <!-- Grafik Bölümü -->
            <section class="chart-section">
                <div class="chart-container">
                    <h2>Balina İşlem Hacmi Trendi</h2>
                    <canvas id="whaleChart"></canvas>
                </div>
            </section>

            <!-- Balina İşlemleri Tablosu -->
            <section class="transactions-section">
                <div class="section-header">
                    <h2>Canlı Balina İşlemleri</h2>
                    <div class="status-indicator">
                        <span class="status-dot"></span>
                        Canlı
                    </div>
                </div>
                <div class="table-container">
                    <table class="whale-table">
                        <thead>
                            <tr>
                                <th>Zaman</th>
                                <th>Kripto Para</th>
                                <th>Miktar</th>
                                <th>USD Değeri</th>
                                <th>İşlem Hash</th>
                                <th>Tip</th>
                                <th>Durum</th>
                            </tr>
                        </thead>
                        <tbody id="whaleTableBody">
                            <!-- JavaScript ile doldurulacak -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Uyarılar Bölümü -->
            <section class="alerts-section">
                <h2>Son Uyarılar</h2>
                <div class="alerts-container" id="alertsContainer">
                    <!-- JavaScript ile doldurulacak -->
                </div>
            </section>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Kripto Balina Takip Sistemi. Tüm hakları saklıdır.</p>
            <p>Veriler simülasyon amaçlıdır. Gerçek işlem verileri değildir.</p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
