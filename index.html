<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kripto Balina Takip Sistemi</title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="Gerçek zamanlı kripto para balina işlemlerini takip edin">
    <meta name="theme-color" content="#00d4ff">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Balina Takip">
    <meta name="msapplication-TileColor" content="#00d4ff">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <i class="fas fa-whale"></i>
                <h1>EMRE Kripto Balina Takip</h1>
                <span class="live-indicator">
                    <i class="fas fa-broadcast-tower"></i>
                    <span>Gerçek Zamanlı</span>
                </span>
            </div>
            <nav class="nav">
                <a href="#dashboard" class="nav-link active">Dashboard</a>
                <a href="#portfolio" class="nav-link">Portföy</a>
                <a href="#analytics" class="nav-link">Analitik</a>
                <a href="#reports" class="nav-link">Raporlar</a>
                <button class="theme-toggle" onclick="toggleTheme()" title="Tema Değiştir">
                    <i class="fas fa-moon" id="themeIcon"></i>
                </button>
                <button class="fullscreen-toggle" onclick="toggleFullscreen()" title="Tam Ekran">
                    <i class="fas fa-expand" id="fullscreenIcon"></i>
                </button>
            </nav>
        </div>
    </header>

    <main class="main">
        <div class="container">
            <!-- İstatistik Kartları -->
            <section class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-whale"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Toplam Balina İşlemi</h3>
                            <p class="stat-number" id="totalWhales">0</p>
                            <span class="stat-change positive">+12%</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Toplam Hacim</h3>
                            <p class="stat-number" id="totalVolume">$0</p>
                            <span class="stat-change positive">+8.5%</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3>En Büyük İşlem</h3>
                            <p class="stat-number" id="largestTx">$0</p>
                            <span class="stat-change negative">-2.1%</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3>Son 24 Saat</h3>
                            <p class="stat-number" id="last24h">0</p>
                            <span class="stat-change positive">+15.3%</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Filtreler -->
            <section class="filters-section">
                <div class="filters">
                    <div class="filter-group">
                        <label for="coinFilter">Kripto Para:</label>
                        <select id="coinFilter">
                            <option value="all">Tümü</option>
                            <optgroup label="Top Kripto Paralar">
                                <option value="BTC">Bitcoin (BTC)</option>
                                <option value="ETH">Ethereum (ETH)</option>
                                <option value="USDT">Tether (USDT)</option>
                                <option value="BNB">BNB (BNB)</option>
                                <option value="SOL">Solana (SOL)</option>
                                <option value="USDC">USD Coin (USDC)</option>
                                <option value="XRP">XRP (XRP)</option>
                                <option value="DOGE">Dogecoin (DOGE)</option>
                                <option value="ADA">Cardano (ADA)</option>
                                <option value="AVAX">Avalanche (AVAX)</option>
                            </optgroup>
                            <optgroup label="DeFi & Altcoinler">
                                <option value="LINK">Chainlink (LINK)</option>
                                <option value="MATIC">Polygon (MATIC)</option>
                                <option value="UNI">Uniswap (UNI)</option>
                                <option value="LTC">Litecoin (LTC)</option>
                                <option value="DOT">Polkadot (DOT)</option>
                                <option value="SHIB">Shiba Inu (SHIB)</option>
                                <option value="WBTC">Wrapped Bitcoin (WBTC)</option>
                                <option value="DAI">Dai (DAI)</option>
                                <option value="ATOM">Cosmos (ATOM)</option>
                                <option value="ETC">Ethereum Classic (ETC)</option>
                                <option value="GALA">Gala (GALA)</option>
                            </optgroup>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="minAmount">Min. Tutar ($):</label>
                        <input type="number" id="minAmount" placeholder="1000000" min="100000">
                    </div>
                    <div class="filter-group">
                        <label for="timeFilter">Zaman:</label>
                        <select id="timeFilter">
                            <option value="1h">Son 1 Saat</option>
                            <option value="24h" selected>Son 24 Saat</option>
                            <option value="7d">Son 7 Gün</option>
                            <option value="30d">Son 30 Gün</option>
                        </select>
                    </div>
                    <button class="filter-btn" onclick="applyFilters()">
                        <i class="fas fa-filter"></i> Filtrele
                    </button>
                </div>
            </section>

            <!-- Grafik Bölümü -->
            <section class="chart-section">
                <div class="chart-container">
                    <h2>Balina İşlem Hacmi Trendi</h2>
                    <canvas id="whaleChart"></canvas>
                </div>
            </section>

            <!-- Balina İşlemleri Tablosu -->
            <section class="transactions-section">
                <div class="section-header">
                    <h2>Canlı Balina İşlemleri</h2>
                    <div class="status-indicator">
                        <span class="status-dot"></span>
                        Canlı
                    </div>
                </div>
                <div class="table-container">
                    <table class="whale-table">
                        <thead>
                            <tr>
                                <th>Zaman</th>
                                <th>Kripto Para</th>
                                <th>Miktar</th>
                                <th>USD Değeri</th>
                                <th>İşlem Hash</th>
                                <th>Tip</th>
                                <th>Durum</th>
                            </tr>
                        </thead>
                        <tbody id="whaleTableBody">
                            <!-- JavaScript ile doldurulacak -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Piyasa Analizi Bölümü -->
            <section class="market-analysis-section">
                <div class="analysis-header-section">
                    <h2>🔮 Balina Analizi & Piyasa Tahmini</h2>
                    <div class="coin-selector">
                        <label for="analysisCoinFilter">Analiz Coin:</label>
                        <select id="analysisCoinFilter" onchange="updateCoinAnalysis()">
                            <option value="all">Genel Piyasa</option>
                            <optgroup label="Top Kripto Paralar">
                                <option value="BTC">Bitcoin (BTC)</option>
                                <option value="ETH">Ethereum (ETH)</option>
                                <option value="USDT">Tether (USDT)</option>
                                <option value="BNB">BNB (BNB)</option>
                                <option value="SOL">Solana (SOL)</option>
                                <option value="USDC">USD Coin (USDC)</option>
                                <option value="XRP">XRP (XRP)</option>
                                <option value="DOGE">Dogecoin (DOGE)</option>
                                <option value="ADA">Cardano (ADA)</option>
                                <option value="AVAX">Avalanche (AVAX)</option>
                            </optgroup>
                            <optgroup label="DeFi & Altcoinler">
                                <option value="LINK">Chainlink (LINK)</option>
                                <option value="MATIC">Polygon (MATIC)</option>
                                <option value="UNI">Uniswap (UNI)</option>
                                <option value="LTC">Litecoin (LTC)</option>
                                <option value="DOT">Polkadot (DOT)</option>
                                <option value="SHIB">Shiba Inu (SHIB)</option>
                                <option value="WBTC">Wrapped Bitcoin (WBTC)</option>
                                <option value="DAI">Dai (DAI)</option>
                                <option value="ATOM">Cosmos (ATOM)</option>
                                <option value="ETC">Ethereum Classic (ETC)</option>
                                <option value="GALA">Gala (GALA)</option>
                            </optgroup>
                        </select>
                    </div>
                </div>
                <div class="analysis-grid">
                    <div class="analysis-card overall-sentiment">
                        <div class="analysis-header">
                            <i class="fas fa-chart-line"></i>
                            <h3 id="sentimentTitle">Genel Piyasa Durumu</h3>
                        </div>
                        <div class="sentiment-indicator" id="overallSentiment">
                            <div class="sentiment-value">Analiz Ediliyor...</div>
                            <div class="sentiment-description">Balina hareketleri analiz ediliyor</div>
                        </div>
                    </div>

                    <div class="analysis-card buy-sell-ratio">
                        <div class="analysis-header">
                            <i class="fas fa-balance-scale"></i>
                            <h3 id="ratioTitle">Alış/Satış Oranı</h3>
                        </div>
                        <div class="ratio-display" id="buySellRatio">
                            <div class="ratio-bars">
                                <div class="buy-bar">
                                    <span>Alış</span>
                                    <div class="bar-fill buy-fill" id="buyBar"></div>
                                    <span id="buyPercentage">0%</span>
                                </div>
                                <div class="sell-bar">
                                    <span>Satış</span>
                                    <div class="bar-fill sell-fill" id="sellBar"></div>
                                    <span id="sellPercentage">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="analysis-card price-prediction">
                        <div class="analysis-header">
                            <i class="fas fa-crystal-ball"></i>
                            <h3 id="predictionTitle">Fiyat Tahmini</h3>
                        </div>
                        <div class="prediction-display" id="pricePrediction">
                            <div class="prediction-direction">
                                <i class="fas fa-arrow-up"></i>
                                <span>Hesaplanıyor...</span>
                            </div>
                            <div class="prediction-confidence">Güven: 0%</div>
                        </div>
                    </div>

                    <div class="analysis-card top-activity">
                        <div class="analysis-header">
                            <i class="fas fa-fire"></i>
                            <h3>En Aktif Coin</h3>
                        </div>
                        <div class="activity-display" id="topActivity">
                            <div class="coin-name">-</div>
                            <div class="activity-count">0 işlem</div>
                            <div class="activity-trend">-</div>
                        </div>
                    </div>
                </div>

                <div class="detailed-analysis" id="detailedAnalysis">
                    <h3 id="detailedAnalysisTitle">📊 Detaylı Analiz</h3>
                    <div class="analysis-text" id="analysisText">
                        Balina hareketleri analiz ediliyor...
                    </div>
                </div>
            </section>

            <!-- Portföy Bölümü -->
            <section class="portfolio-section" id="portfolioSection" style="display: none;">
                <div class="section-header">
                    <h2>💼 Kişisel Portföy</h2>
                    <button class="add-coin-btn" onclick="showAddCoinModal()">
                        <i class="fas fa-plus"></i> Coin Ekle
                    </button>
                </div>

                <div class="portfolio-summary">
                    <div class="portfolio-card">
                        <h3>Toplam Değer</h3>
                        <p class="portfolio-value" id="totalPortfolioValue">$0</p>
                        <span class="portfolio-change" id="totalPortfolioChange">+0%</span>
                    </div>
                    <div class="portfolio-card">
                        <h3>Günlük P&L</h3>
                        <p class="portfolio-pnl" id="dailyPnL">$0</p>
                        <span class="pnl-percentage" id="dailyPnLPercentage">+0%</span>
                    </div>
                    <div class="portfolio-card">
                        <h3>En İyi Performans</h3>
                        <p class="best-performer" id="bestPerformer">-</p>
                        <span class="performer-change" id="bestPerformerChange">+0%</span>
                    </div>
                </div>

                <div class="portfolio-table-container">
                    <table class="portfolio-table">
                        <thead>
                            <tr>
                                <th>Coin</th>
                                <th>Miktar</th>
                                <th>Ortalama Fiyat</th>
                                <th>Güncel Fiyat</th>
                                <th>Değer</th>
                                <th>P&L</th>
                                <th>%</th>
                                <th>İşlem</th>
                            </tr>
                        </thead>
                        <tbody id="portfolioTableBody">
                            <!-- JavaScript ile doldurulacak -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Teknik Analiz Bölümü -->
            <section class="technical-analysis-section" id="technicalSection" style="display: none;">
                <h2>📈 Teknik Analiz</h2>
                <div class="technical-grid">
                    <div class="technical-card">
                        <h3>RSI (14)</h3>
                        <div class="rsi-indicator" id="rsiIndicator">
                            <div class="rsi-bar">
                                <div class="rsi-fill" id="rsiFill"></div>
                            </div>
                            <span class="rsi-value" id="rsiValue">50</span>
                        </div>
                    </div>
                    <div class="technical-card">
                        <h3>MACD</h3>
                        <div class="macd-display" id="macdDisplay">
                            <div class="macd-line">MACD: <span id="macdValue">0</span></div>
                            <div class="signal-line">Signal: <span id="signalValue">0</span></div>
                            <div class="histogram">Histogram: <span id="histogramValue">0</span></div>
                        </div>
                    </div>
                    <div class="technical-card">
                        <h3>Bollinger Bands</h3>
                        <div class="bollinger-display" id="bollingerDisplay">
                            <div>Üst: <span id="upperBand">$0</span></div>
                            <div>Orta: <span id="middleBand">$0</span></div>
                            <div>Alt: <span id="lowerBand">$0</span></div>
                        </div>
                    </div>
                    <div class="technical-card">
                        <h3>Support/Resistance</h3>
                        <div class="sr-levels" id="srLevels">
                            <div class="resistance">R1: <span id="resistance1">$0</span></div>
                            <div class="support">S1: <span id="support1">$0</span></div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Uyarılar Bölümü -->
            <section class="alerts-section">
                <h2>Son Uyarılar</h2>
                <div class="alerts-container" id="alertsContainer">
                    <!-- JavaScript ile doldurulacak -->
                </div>
            </section>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Kripto Balina Takip Sistemi. Tüm hakları saklıdır.</p>
            <p><strong>🌐 Gerçek Zamanlı Veriler:</strong> Fiyatlar CoinGecko API'den alınmaktadır. Balina işlemleri gerçek fiyatlarla simüle edilmektedir.</p>
            <p><small>⚠️ Yatırım tavsiyesi değildir. Sadece eğitim ve analiz amaçlıdır.</small></p>
        </div>
    </footer>

    <!-- Coin Ekleme Modal -->
    <div class="modal" id="addCoinModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Portföye Coin Ekle</h3>
                <span class="close" onclick="closeAddCoinModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="addCoinForm">
                    <div class="form-group">
                        <label for="modalCoinSelect">Coin Seç:</label>
                        <select id="modalCoinSelect" required>
                            <option value="">Coin seçin...</option>
                            <option value="BTC">Bitcoin (BTC)</option>
                            <option value="ETH">Ethereum (ETH)</option>
                            <option value="BNB">BNB (BNB)</option>
                            <option value="SOL">Solana (SOL)</option>
                            <option value="ADA">Cardano (ADA)</option>
                            <option value="DOGE">Dogecoin (DOGE)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="coinAmount">Miktar:</label>
                        <input type="number" id="coinAmount" step="0.00000001" required>
                    </div>
                    <div class="form-group">
                        <label for="buyPrice">Alış Fiyatı ($):</label>
                        <input type="number" id="buyPrice" step="0.01" required>
                    </div>
                    <div class="form-actions">
                        <button type="button" onclick="closeAddCoinModal()">İptal</button>
                        <button type="submit">Ekle</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Raporlama Bölümü -->
    <div class="reports-section" id="reportsSection" style="display: none;">
        <h2>📊 Raporlar</h2>
        <div class="reports-grid">
            <div class="report-card">
                <h3>Günlük Rapor</h3>
                <p>Son 24 saatlik balina aktivitesi</p>
                <button onclick="generateDailyReport()">PDF İndir</button>
            </div>
            <div class="report-card">
                <h3>Haftalık Rapor</h3>
                <p>Son 7 günlük trend analizi</p>
                <button onclick="generateWeeklyReport()">PDF İndir</button>
            </div>
            <div class="report-card">
                <h3>Portföy Raporu</h3>
                <p>Kişisel portföy performansı</p>
                <button onclick="generatePortfolioReport()">PDF İndir</button>
            </div>
        </div>
    </div>

    <!-- PWA Install Banner -->
    <div class="install-banner" id="installBanner" style="display: none;">
        <div class="install-content">
            <i class="fas fa-mobile-alt"></i>
            <div>
                <h4>Uygulamayı Yükle</h4>
                <p>Daha iyi deneyim için telefona yükleyin</p>
            </div>
            <button onclick="installPWA()">Yükle</button>
            <button onclick="dismissInstallBanner()">&times;</button>
        </div>
    </div>

    <script src="script.js"></script>

    <!-- PWA Registration -->
    <script>
        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }

        // PWA Install Prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            document.getElementById('installBanner').style.display = 'block';
        });

        function installPWA() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('User accepted the install prompt');
                    }
                    deferredPrompt = null;
                    document.getElementById('installBanner').style.display = 'none';
                });
            }
        }

        function dismissInstallBanner() {
            document.getElementById('installBanner').style.display = 'none';
        }
    </script>
</body>
</html>
