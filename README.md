# Kripto Para Balina Takip Sistemi

Bu proje, kripto para borsalarında gerçekleşen büyük hacimli işlemleri (balina işlemleri) tespit eden ve görselleştiren bir web uygulamasıdır.

## Özellikler

### 🐋 Balina Tespiti
- Büyük hacimli kripto para işlemlerini otomatik tespit
- Minimum 100K USD değerinde işlemler için uyarı sistemi
- Gerçek zamanlı işlem takibi

### 📊 Görselleştirme
- İnteraktif grafikler ile işlem hacmi trendi
- Saatlik, günlük ve haftalık analiz
- Responsive tasarım ile mobil uyumluluk

### 🔍 Filtreleme
- Kripto para türüne göre filtreleme (BTC, ETH, BNB, ADA, SOL)
- Minimum işlem tutarı belirleme
- Zaman aralığı seçimi (1 saat - 30 gün)

### ⚡ Canlı Takip
- 5 saniyede bir otomatik güncelleme
- Gerçek zamanlı uyarı sistemi
- İşlem durumu takibi (Onaylandı/Beklemede)

## Teknolojiler

- **HTML5**: Semantik yapı ve erişilebilirlik
- **CSS3**: Modern tasarım, gradyanlar, animasyonlar
- **JavaScript (ES6+)**: Dinamik içerik ve veri işleme
- **Chart.js**: İnteraktif grafikler
- **Font Awesome**: İkonlar

## Kullanım

1. `index.html` dosyasını bir web tarayıcısında açın
2. Dashboard üzerinde anlık balina işlemlerini görüntüleyin
3. Filtreleri kullanarak istediğiniz kriterlere göre işlemleri süzün
4. Grafik üzerinde işlem hacmi trendlerini analiz edin
5. Uyarılar bölümünden büyük işlemleri takip edin

## Veri Simülasyonu

Bu uygulama eğitim ve demo amaçlı olarak tasarlanmıştır. Tüm veriler JavaScript ile simüle edilmektedir:

- **Kripto Paralar**: Bitcoin (BTC), Ethereum (ETH), Binance Coin (BNB), Cardano (ADA), Solana (SOL)
- **İşlem Tutarları**: 100K USD - 10M USD arası rastgele değerler
- **İşlem Tipleri**: Alış/Satış işlemleri
- **Zaman Damgaları**: Son 24 saat içinde rastgele zamanlar

## Özelleştirme

### Yeni Kripto Para Ekleme
`script.js` dosyasındaki `cryptoData` objesine yeni kripto para ekleyebilirsiniz:

```javascript
const cryptoData = {
    'BTC': { name: 'Bitcoin', price: 43250, symbol: '₿' },
    'ETH': { name: 'Ethereum', price: 2580, symbol: 'Ξ' },
    // Yeni kripto para buraya eklenebilir
    'DOGE': { name: 'Dogecoin', price: 0.08, symbol: 'DOGE' }
};
```

### Minimum Balina Tutarını Değiştirme
`generateRandomTransaction()` fonksiyonundaki `minAmounts` objesini düzenleyerek minimum tutarları değiştirebilirsiniz.

### Güncelleme Sıklığını Ayarlama
`initializeApp()` fonksiyonundaki interval süresini değiştirerek güncelleme sıklığını ayarlayabilirsiniz:

```javascript
// 5000ms = 5 saniye (varsayılan)
updateInterval = setInterval(() => {
    // ...
}, 5000);
```

## Tarayıcı Uyumluluğu

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Lisans

Bu proje eğitim amaçlı olarak geliştirilmiştir. Ticari kullanım için uygun değildir.

## Uyarı

Bu uygulama sadece demo amaçlıdır. Gerçek kripto para işlem verileri kullanmamaktadır. Yatırım kararları için kullanılmamalıdır.
