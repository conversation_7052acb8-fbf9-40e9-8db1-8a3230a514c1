# Kripto Para Balina Takip Sistemi - Gerçek Zamanlı

Bu proje, kripto para borsalarında gerçekleşen büyük hacimli işlemleri (balina işlemleri) tespit eden ve görselleştiren bir web uygulamasıdır. **Gerçek zamanlı CoinGecko API verilerini kullanır.**

## 🚀 Yeni Özellikler (Gerçek Zamanlı)

### 🌐 Gerçek API Entegrasyonu
- **CoinGecko API** ile gerçek zamanlı fiyat verileri
- 30 saniyede bir otomatik fiyat güncellemesi
- Gerçek piyasa hacmi ve değişim oranları
- API bağlantı durumu göstergesi

### 🐋 Gelişmiş Balina Tespiti
- Gerçek piyasa hacmine dayalı işlem boyutları
- Büyük işlemler için anlık popup uyarıları
- 10M USD+ işlemler için özel mega balina uyarıları
- Gerçekçi zaman dağılımı algoritması

### 📊 Gelişmiş Görselleştirme
- Gerçek fiyat verilerine dayalı grafikler
- Canlı güncellenen istatistikler
- Piyasa değişim oranları gösterimi
- Responsive tasarım ile mobil uyumluluk

### 🔍 Akıllı Filtreleme
- Kripto para türüne göre filtreleme (BTC, ETH, BNB, ADA, SOL)
- Minimum işlem tutarı belirleme
- Zaman aralığı seçimi (1 saat - 30 gün)
- Gerçek zamanlı filtreleme

### ⚡ Performans Optimizasyonu
- Sayfa gizlendiğinde güncelleme sıklığını azaltma
- Akıllı veri yönetimi (maksimum 100 işlem)
- Hata durumunda otomatik geri dönüş
- Bağlantı durumu takibi

## Teknolojiler

- **HTML5**: Semantik yapı ve erişilebilirlik
- **CSS3**: Modern tasarım, gradyanlar, animasyonlar
- **JavaScript (ES6+)**: Async/await, Fetch API, ES6 modülleri
- **CoinGecko API**: Gerçek zamanlı kripto para verileri
- **Chart.js**: İnteraktif grafikler
- **Font Awesome**: İkonlar

## Kullanım

1. `index.html` dosyasını bir web tarayıcısında açın
2. Dashboard üzerinde anlık balina işlemlerini görüntüleyin
3. Filtreleri kullanarak istediğiniz kriterlere göre işlemleri süzün
4. Grafik üzerinde işlem hacmi trendlerini analiz edin
5. Uyarılar bölümünden büyük işlemleri takip edin

## 🔄 Gerçek Zamanlı Veri Sistemi

Bu uygulama **gerçek kripto para verilerini** kullanır:

### 📡 Veri Kaynakları
- **Fiyat Verileri**: CoinGecko API'den gerçek zamanlı
- **Piyasa Hacmi**: Güncel 24 saatlik işlem hacmi
- **Değişim Oranları**: Son 24 saatteki fiyat değişimleri
- **Piyasa Değeri**: Güncel market cap verileri

### 🎯 Balina İşlem Simülasyonu
- **Gerçek Fiyatlar**: API'den alınan güncel fiyatlarla hesaplama
- **Piyasa Hacmi Bazlı**: Günlük hacmin %5'ine kadar işlemler
- **Akıllı Algoritma**: Gerçekçi zaman dağılımı ve işlem boyutları
- **Minimum Tutarlar**:
  - Bitcoin: 1M USD+
  - Ethereum: 500K USD+
  - Binance Coin: 200K USD+
  - Cardano: 100K USD+
  - Solana: 150K USD+

### ⚠️ Önemli Not
Fiyat verileri gerçektir, ancak balina işlemleri simüle edilmiştir. Gerçek blockchain işlemleri değildir.

## Özelleştirme

### Yeni Kripto Para Ekleme
`script.js` dosyasındaki `cryptoData` objesine yeni kripto para ekleyebilirsiniz:

```javascript
const cryptoData = {
    'BTC': { name: 'Bitcoin', price: 43250, symbol: '₿' },
    'ETH': { name: 'Ethereum', price: 2580, symbol: 'Ξ' },
    // Yeni kripto para buraya eklenebilir
    'DOGE': { name: 'Dogecoin', price: 0.08, symbol: 'DOGE' }
};
```

### Minimum Balina Tutarını Değiştirme
`generateRandomTransaction()` fonksiyonundaki `minAmounts` objesini düzenleyerek minimum tutarları değiştirebilirsiniz.

### Güncelleme Sıklığını Ayarlama
`initializeApp()` fonksiyonundaki interval süresini değiştirerek güncelleme sıklığını ayarlayabilirsiniz:

```javascript
// 5000ms = 5 saniye (varsayılan)
updateInterval = setInterval(() => {
    // ...
}, 5000);
```

## Tarayıcı Uyumluluğu

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Lisans

Bu proje eğitim amaçlı olarak geliştirilmiştir. Ticari kullanım için uygun değildir.

## Uyarı

Bu uygulama sadece demo amaçlıdır. Gerçek kripto para işlem verileri kullanmamaktadır. Yatırım kararları için kullanılmamalıdır.
