/* Reset ve <PERSON><PERSON> */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    color: #ffffff;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo i {
    font-size: 2rem;
    color: #00d4ff;
}

.logo h1 {
    font-size: 1.5rem;
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
}

/* Main Content */
.main {
    padding: 2rem 0;
}

/* İstatistik Kartları */
.stats-section {
    margin-bottom: 3rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.stat-card:hover {
    transform: translateY(-5px);
    border-color: rgba(0, 212, 255, 0.3);
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.1);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-content h3 {
    font-size: 0.9rem;
    color: #cccccc;
    margin-bottom: 0.5rem;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: 0.8rem;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
}

.stat-change.positive {
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
}

.stat-change.negative {
    background: rgba(255, 0, 0, 0.2);
    color: #ff4444;
}

/* Filtreler */
.filters-section {
    margin-bottom: 3rem;
}

.filters {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-size: 0.9rem;
    color: #cccccc;
}

.filter-group select,
.filter-group input {
    padding: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.3);
    color: white;
    min-width: 150px;
}

.filter-btn {
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

/* Grafik */
.chart-section {
    margin-bottom: 3rem;
}

.chart-container {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
}

.chart-container h2 {
    margin-bottom: 1rem;
    color: #00d4ff;
}

/* Tablo */
.transactions-section {
    margin-bottom: 3rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #00ff00;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #00ff00;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.table-container {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    overflow: hidden;
}

.whale-table {
    width: 100%;
    border-collapse: collapse;
}

.whale-table th {
    background: rgba(0, 212, 255, 0.1);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: #00d4ff;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.whale-table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.whale-table tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

.transaction-type {
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.transaction-type.buy {
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
}

.transaction-type.sell {
    background: rgba(255, 0, 0, 0.2);
    color: #ff4444;
}

.transaction-status {
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
}

.transaction-status.confirmed {
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
}

.transaction-status.pending {
    background: rgba(255, 165, 0, 0.2);
    color: #ffa500;
}

/* Uyarılar */
.alerts-section {
    margin-bottom: 3rem;
}

.alerts-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.alert {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.alert.high {
    border-color: rgba(255, 0, 0, 0.3);
    background: rgba(255, 0, 0, 0.1);
}

.alert.medium {
    border-color: rgba(255, 165, 0, 0.3);
    background: rgba(255, 165, 0, 0.1);
}

.alert-icon {
    font-size: 1.5rem;
}

.alert.high .alert-icon {
    color: #ff4444;
}

.alert.medium .alert-icon {
    color: #ffa500;
}

/* Footer */
.footer {
    background: rgba(0, 0, 0, 0.8);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    text-align: center;
    color: #cccccc;
}

/* Responsive */
@media (max-width: 768px) {
    .filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .whale-table {
        font-size: 0.8rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}
