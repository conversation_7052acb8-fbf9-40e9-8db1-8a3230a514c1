// Kripto Balina Takip Sistemi JavaScript

// Global değişkenler
let whaleTransactions = [];
let chart;
let updateInterval;

// Kripto para verileri
const cryptoData = {
    'BTC': { name: 'Bitcoin', price: 43250, symbol: '₿' },
    'ETH': { name: 'Ethereum', price: 2580, symbol: 'Ξ' },
    'BNB': { name: 'Binance Coin', price: 315, symbol: 'BNB' },
    'ADA': { name: 'Cardano', price: 0.48, symbol: 'ADA' },
    'SOL': { name: '<PERSON><PERSON>', price: 98, symbol: 'SOL' }
};

// Sayfa yüklendiğinde çalışacak fonksiyon
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    generateInitialData();
    updateStatistics();
    createChart();
    updateTransactionTable();
    updateAlerts();
    
    // Otomatik güncelleme (5 saniyede bir)
    updateInterval = setInterval(() => {
        generateNewTransaction();
        updateStatistics();
        updateChart();
        updateTransactionTable();
        updateAlerts();
    }, 5000);
}

// İlk veri setini oluştur
function generateInitialData() {
    for (let i = 0; i < 20; i++) {
        whaleTransactions.push(generateRandomTransaction());
    }
    // Tarihe göre sırala (en yeni önce)
    whaleTransactions.sort((a, b) => b.timestamp - a.timestamp);
}

// Rastgele balina işlemi oluştur
function generateRandomTransaction() {
    const coins = Object.keys(cryptoData);
    const coin = coins[Math.floor(Math.random() * coins.length)];
    const coinInfo = cryptoData[coin];
    
    // Balina işlemi için minimum tutarlar (USD)
    const minAmounts = {
        'BTC': 1000000, // 1M USD
        'ETH': 500000,  // 500K USD
        'BNB': 200000,  // 200K USD
        'ADA': 100000,  // 100K USD
        'SOL': 150000   // 150K USD
    };
    
    const minUsdAmount = minAmounts[coin];
    const maxUsdAmount = minUsdAmount * 10;
    const usdValue = Math.random() * (maxUsdAmount - minUsdAmount) + minUsdAmount;
    const amount = usdValue / coinInfo.price;
    
    const types = ['buy', 'sell'];
    const type = types[Math.floor(Math.random() * types.length)];
    
    const statuses = ['confirmed', 'pending'];
    const status = Math.random() > 0.2 ? 'confirmed' : 'pending';
    
    return {
        id: generateTxHash(),
        timestamp: Date.now() - Math.random() * 86400000, // Son 24 saat içinde
        coin: coin,
        amount: amount,
        usdValue: usdValue,
        type: type,
        status: status,
        hash: generateTxHash()
    };
}

// Yeni işlem oluştur
function generateNewTransaction() {
    const newTx = generateRandomTransaction();
    newTx.timestamp = Date.now(); // Şu anki zaman
    whaleTransactions.unshift(newTx);
    
    // En fazla 100 işlem tut
    if (whaleTransactions.length > 100) {
        whaleTransactions = whaleTransactions.slice(0, 100);
    }
}

// İşlem hash'i oluştur
function generateTxHash() {
    const chars = '0123456789abcdef';
    let hash = '0x';
    for (let i = 0; i < 64; i++) {
        hash += chars[Math.floor(Math.random() * chars.length)];
    }
    return hash;
}

// İstatistikleri güncelle
function updateStatistics() {
    const now = Date.now();
    const last24h = now - 86400000;
    
    const recent24h = whaleTransactions.filter(tx => tx.timestamp > last24h);
    const totalWhales = recent24h.length;
    const totalVolume = recent24h.reduce((sum, tx) => sum + tx.usdValue, 0);
    const largestTx = Math.max(...recent24h.map(tx => tx.usdValue));
    
    document.getElementById('totalWhales').textContent = totalWhales.toLocaleString();
    document.getElementById('totalVolume').textContent = '$' + formatNumber(totalVolume);
    document.getElementById('largestTx').textContent = '$' + formatNumber(largestTx);
    document.getElementById('last24h').textContent = totalWhales.toLocaleString();
}

// Sayı formatlama
function formatNumber(num) {
    if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
    if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
    if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
    return num.toFixed(2);
}

// Grafik oluştur
function createChart() {
    const ctx = document.getElementById('whaleChart').getContext('2d');
    
    // Son 24 saatlik veriyi saatlik gruplara böl
    const hourlyData = getHourlyData();
    
    chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: hourlyData.labels,
            datasets: [{
                label: 'Balina İşlem Hacmi (USD)',
                data: hourlyData.values,
                borderColor: '#00d4ff',
                backgroundColor: 'rgba(0, 212, 255, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    labels: {
                        color: '#ffffff'
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: '#cccccc'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                y: {
                    ticks: {
                        color: '#cccccc',
                        callback: function(value) {
                            return '$' + formatNumber(value);
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            }
        }
    });
}

// Saatlik veri hazırla
function getHourlyData() {
    const now = Date.now();
    const hours = [];
    const values = [];
    
    for (let i = 23; i >= 0; i--) {
        const hourStart = now - (i * 3600000);
        const hourEnd = hourStart + 3600000;
        
        const hourTxs = whaleTransactions.filter(tx => 
            tx.timestamp >= hourStart && tx.timestamp < hourEnd
        );
        
        const hourVolume = hourTxs.reduce((sum, tx) => sum + tx.usdValue, 0);
        
        const date = new Date(hourStart);
        hours.push(date.getHours() + ':00');
        values.push(hourVolume);
    }
    
    return { labels: hours, values: values };
}

// Grafik güncelle
function updateChart() {
    if (chart) {
        const hourlyData = getHourlyData();
        chart.data.labels = hourlyData.labels;
        chart.data.datasets[0].data = hourlyData.values;
        chart.update('none');
    }
}

// İşlem tablosunu güncelle
function updateTransactionTable() {
    const tbody = document.getElementById('whaleTableBody');
    const filteredTxs = getFilteredTransactions();
    
    tbody.innerHTML = '';
    
    filteredTxs.slice(0, 20).forEach(tx => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${formatTime(tx.timestamp)}</td>
            <td>
                <strong>${tx.coin}</strong><br>
                <small>${cryptoData[tx.coin].name}</small>
            </td>
            <td>${formatNumber(tx.amount)} ${cryptoData[tx.coin].symbol}</td>
            <td>$${formatNumber(tx.usdValue)}</td>
            <td>
                <code style="font-size: 0.8rem;">
                    ${tx.hash.substring(0, 10)}...${tx.hash.substring(tx.hash.length - 8)}
                </code>
            </td>
            <td>
                <span class="transaction-type ${tx.type}">
                    ${tx.type === 'buy' ? 'ALIŞ' : 'SATIŞ'}
                </span>
            </td>
            <td>
                <span class="transaction-status ${tx.status}">
                    ${tx.status === 'confirmed' ? 'Onaylandı' : 'Beklemede'}
                </span>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Filtrelenmiş işlemleri getir
function getFilteredTransactions() {
    const coinFilter = document.getElementById('coinFilter').value;
    const minAmount = parseFloat(document.getElementById('minAmount').value) || 0;
    const timeFilter = document.getElementById('timeFilter').value;
    
    let filtered = whaleTransactions;
    
    // Coin filtresi
    if (coinFilter !== 'all') {
        filtered = filtered.filter(tx => tx.coin === coinFilter);
    }
    
    // Minimum tutar filtresi
    if (minAmount > 0) {
        filtered = filtered.filter(tx => tx.usdValue >= minAmount);
    }
    
    // Zaman filtresi
    const now = Date.now();
    let timeLimit;
    switch (timeFilter) {
        case '1h':
            timeLimit = now - 3600000;
            break;
        case '24h':
            timeLimit = now - 86400000;
            break;
        case '7d':
            timeLimit = now - 604800000;
            break;
        case '30d':
            timeLimit = now - 2592000000;
            break;
        default:
            timeLimit = 0;
    }
    
    filtered = filtered.filter(tx => tx.timestamp >= timeLimit);
    
    return filtered;
}

// Zaman formatlama
function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'Az önce';
    if (diff < 3600000) return Math.floor(diff / 60000) + ' dk önce';
    if (diff < 86400000) return Math.floor(diff / 3600000) + ' sa önce';
    
    return date.toLocaleDateString('tr-TR') + ' ' + date.toLocaleTimeString('tr-TR', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Uyarıları güncelle
function updateAlerts() {
    const container = document.getElementById('alertsContainer');
    const recentLarge = whaleTransactions
        .filter(tx => tx.timestamp > Date.now() - 3600000 && tx.usdValue > 5000000)
        .slice(0, 5);
    
    container.innerHTML = '';
    
    recentLarge.forEach(tx => {
        const alert = document.createElement('div');
        const severity = tx.usdValue > 10000000 ? 'high' : 'medium';
        
        alert.className = `alert ${severity}`;
        alert.innerHTML = `
            <div class="alert-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="alert-content">
                <strong>Büyük ${tx.type === 'buy' ? 'Alış' : 'Satış'} İşlemi Tespit Edildi!</strong><br>
                ${formatNumber(tx.amount)} ${cryptoData[tx.coin].symbol} (${tx.coin}) - 
                $${formatNumber(tx.usdValue)} - ${formatTime(tx.timestamp)}
            </div>
        `;
        
        container.appendChild(alert);
    });
    
    if (recentLarge.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #cccccc;">Son 1 saatte büyük işlem tespit edilmedi.</p>';
    }
}

// Filtreleri uygula
function applyFilters() {
    updateTransactionTable();
}

// Sayfa kapatılırken interval'ı temizle
window.addEventListener('beforeunload', function() {
    if (updateInterval) {
        clearInterval(updateInterval);
    }
});
