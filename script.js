// Kripto Balina Takip Sistemi JavaScript - Gerçek Zamanlı Veriler

// Global değişkenler
let whaleTransactions = [];
let chart;
let updateInterval;
let priceUpdateInterval;
let realTimeData = {};
let portfolio = [];
let currentSection = 'dashboard';
let technicalData = {};
let gameStats = {
    score: 0,
    level: 1,
    predictions: 0,
    correctPredictions: 0
};

// Kripto para verileri - Gerçek zamanlı güncellenecek
const cryptoData = {
    // Top 10 Kripto Paralar
    'bitcoin': {
        name: 'Bitcoin',
        symbol: 'BTC',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: '₿'
    },
    'ethereum': {
        name: 'Ethereum',
        symbol: 'ETH',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'Ξ'
    },
    'tether': {
        name: 'Tether',
        symbol: 'USDT',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'USDT'
    },
    'binancecoin': {
        name: 'BNB',
        symbol: 'BNB',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'BNB'
    },
    'solana': {
        name: 'Solana',
        symbol: 'SOL',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'SOL'
    },
    'usd-coin': {
        name: 'USD Coin',
        symbol: 'USDC',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'USDC'
    },
    'xrp': {
        name: 'XRP',
        symbol: 'XRP',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'XRP'
    },
    'dogecoin': {
        name: 'Dogecoin',
        symbol: 'DOGE',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'DOGE'
    },
    'cardano': {
        name: 'Cardano',
        symbol: 'ADA',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'ADA'
    },
    'avalanche-2': {
        name: 'Avalanche',
        symbol: 'AVAX',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'AVAX'
    },
    // DeFi ve Layer 2 Tokenları
    'chainlink': {
        name: 'Chainlink',
        symbol: 'LINK',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'LINK'
    },
    'polygon': {
        name: 'Polygon',
        symbol: 'MATIC',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'MATIC'
    },
    'uniswap': {
        name: 'Uniswap',
        symbol: 'UNI',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'UNI'
    },
    'litecoin': {
        name: 'Litecoin',
        symbol: 'LTC',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'LTC'
    },
    'polkadot': {
        name: 'Polkadot',
        symbol: 'DOT',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'DOT'
    },
    'shiba-inu': {
        name: 'Shiba Inu',
        symbol: 'SHIB',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'SHIB'
    },
    'wrapped-bitcoin': {
        name: 'Wrapped Bitcoin',
        symbol: 'WBTC',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'WBTC'
    },
    'dai': {
        name: 'Dai',
        symbol: 'DAI',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'DAI'
    },
    'cosmos': {
        name: 'Cosmos',
        symbol: 'ATOM',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'ATOM'
    },
    'ethereum-classic': {
        name: 'Ethereum Classic',
        symbol: 'ETC',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'ETC'
    },
    'gala': {
        name: 'Gala',
        symbol: 'GALA',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'GALA'
    }
};

// API URL'leri
const COINGECKO_API = 'https://api.coingecko.com/api/v3';
const CORS_PROXY = 'https://api.allorigins.win/raw?url=';

// Balina tespiti için minimum tutarlar (USD)
const WHALE_THRESHOLDS = {
    // Büyük Kripto Paralar
    'bitcoin': 1000000,        // 1M USD
    'ethereum': 500000,        // 500K USD
    'tether': 2000000,         // 2M USD (stablecoin)
    'binancecoin': 200000,     // 200K USD
    'solana': 150000,          // 150K USD
    'usd-coin': 2000000,       // 2M USD (stablecoin)
    'xrp': 100000,             // 100K USD
    'dogecoin': 50000,         // 50K USD
    'cardano': 100000,         // 100K USD
    'avalanche-2': 100000,     // 100K USD

    // DeFi ve Altcoinler
    'chainlink': 75000,        // 75K USD
    'polygon': 50000,          // 50K USD
    'uniswap': 75000,          // 75K USD
    'litecoin': 100000,        // 100K USD
    'polkadot': 75000,         // 75K USD
    'shiba-inu': 25000,        // 25K USD
    'wrapped-bitcoin': 1000000, // 1M USD
    'dai': 1000000,            // 1M USD (stablecoin)
    'cosmos': 50000,           // 50K USD
    'ethereum-classic': 50000, // 50K USD
    'gala': 30000              // 30K USD (gaming token)
};

// Sayfa yüklendiğinde çalışacak fonksiyon
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

async function initializeApp() {
    showLoadingMessage();

    try {
        console.log('Uygulama başlatılıyor...');

        // Tema yükle
        loadTheme();

        // Portföy yükle
        loadPortfolio();

        // Gerçek fiyat verilerini al
        await fetchRealTimePrices();
        console.log('Fiyat verileri alındı:', cryptoData);

        // İlk veri setini oluştur
        generateInitialData();
        console.log('İlk veriler oluşturuldu, işlem sayısı:', whaleTransactions.length);

        updateStatistics();
        createChart();
        updateTransactionTable();
        updateAlerts();
        updateMarketAnalysis();

        hideLoadingMessage();
        console.log('Uygulama başarıyla başlatıldı!');

        // Fiyatları her 30 saniyede bir güncelle
        priceUpdateInterval = setInterval(async () => {
            try {
                await fetchRealTimePrices();
                updateStatistics();
                if (currentSection === 'portfolio') {
                    updatePortfolio();
                }
                if (currentSection === 'analytics') {
                    updateTechnicalAnalysis();
                }
            } catch (error) {
                console.error('Fiyat güncelleme hatası:', error);
            }
        }, 30000);

        // Balina işlemlerini her 10 saniyede bir güncelle
        updateInterval = setInterval(() => {
            try {
                generateNewTransaction();
                updateStatistics();
                updateChart();
                updateTransactionTable();
                updateAlerts();
                updateMarketAnalysis();
            } catch (error) {
                console.error('İşlem güncelleme hatası:', error);
            }
        }, 10000);

    } catch (error) {
        console.error('Uygulama başlatılırken hata:', error);
        hideLoadingMessage();
        showErrorMessage('Veriler yüklenirken hata oluştu. Lütfen sayfayı yenileyin.');
    }
}

// Gerçek zamanlı fiyat verilerini çek
async function fetchRealTimePrices() {
    try {
        const coinIds = Object.keys(cryptoData).join(',');
        const url = `${COINGECKO_API}/simple/price?ids=${coinIds}&vs_currencies=usd&include_24hr_change=true&include_24hr_vol=true&include_market_cap=true`;

        const response = await fetch(url);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // Veriyi güncelle
        Object.keys(cryptoData).forEach(coinId => {
            if (data[coinId]) {
                cryptoData[coinId].price = data[coinId].usd || 0;
                cryptoData[coinId].change24h = data[coinId].usd_24h_change || 0;
                cryptoData[coinId].volume24h = data[coinId].usd_24h_vol || 0;
                cryptoData[coinId].marketCap = data[coinId].usd_market_cap || 0;
            }
        });

        console.log('Fiyat verileri güncellendi:', new Date().toLocaleTimeString());

    } catch (error) {
        console.error('Fiyat verileri alınırken hata:', error);
        // Hata durumunda varsayılan değerleri kullan
        if (Object.values(cryptoData).every(coin => coin.price === 0)) {
            setDefaultPrices();
        }
    }
}

// Varsayılan fiyatları ayarla (API erişimi olmadığında)
function setDefaultPrices() {
    // Top kripto paralar
    cryptoData.bitcoin.price = 108000;
    cryptoData.ethereum.price = 2640;
    cryptoData.tether.price = 1.00;
    cryptoData.binancecoin.price = 684;
    cryptoData.solana.price = 175;
    cryptoData['usd-coin'].price = 1.00;
    cryptoData.xrp.price = 0.75;
    cryptoData.dogecoin.price = 0.08;
    cryptoData.cardano.price = 0.75;
    cryptoData['avalanche-2'].price = 35;

    // DeFi ve altcoinler
    cryptoData.chainlink.price = 15;
    cryptoData.polygon.price = 0.45;
    cryptoData.uniswap.price = 8.5;
    cryptoData.litecoin.price = 85;
    cryptoData.polkadot.price = 6.5;
    cryptoData['shiba-inu'].price = 0.000009;
    cryptoData['wrapped-bitcoin'].price = 108000;
    cryptoData.dai.price = 1.00;
    cryptoData.cosmos.price = 7.2;
    cryptoData['ethereum-classic'].price = 22;
    cryptoData.gala.price = 0.025;

    console.log('Varsayılan fiyatlar kullanılıyor');
}

// Yükleme mesajı göster
function showLoadingMessage() {
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'loadingMessage';
    loadingDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.9);
        color: #00d4ff;
        padding: 2rem;
        border-radius: 10px;
        text-align: center;
        z-index: 1000;
        border: 1px solid rgba(0, 212, 255, 0.3);
    `;
    loadingDiv.innerHTML = `
        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 1rem;"></i>
        <h3>Gerçek Zamanlı Veriler Yükleniyor...</h3>
        <p>CoinGecko API'den güncel fiyatlar alınıyor</p>
    `;
    document.body.appendChild(loadingDiv);
}

// Yükleme mesajını gizle
function hideLoadingMessage() {
    const loadingDiv = document.getElementById('loadingMessage');
    if (loadingDiv) {
        loadingDiv.remove();
    }
}

// Hata mesajı göster
function showErrorMessage(message) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(255, 0, 0, 0.9);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        z-index: 1000;
        max-width: 300px;
    `;
    errorDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i> ${message}
    `;
    document.body.appendChild(errorDiv);

    setTimeout(() => errorDiv.remove(), 5000);
}

// İlk veri setini oluştur
function generateInitialData() {
    whaleTransactions = []; // Önce temizle

    for (let i = 0; i < 20; i++) {
        const newTx = generateRandomTransaction();
        if (newTx) { // Null kontrolü
            whaleTransactions.push(newTx);
        }
    }

    // Tarihe göre sırala (en yeni önce)
    whaleTransactions.sort((a, b) => b.timestamp - a.timestamp);

    console.log(`${whaleTransactions.length} adet başlangıç işlemi oluşturuldu`);
}

// Gerçek verilerle balina işlemi oluştur
function generateRandomTransaction() {
    const coinIds = Object.keys(cryptoData);
    const coinId = coinIds[Math.floor(Math.random() * coinIds.length)];
    const coinInfo = cryptoData[coinId];

    // Gerçek fiyat kontrolü
    if (!coinInfo.price || coinInfo.price === 0) {
        console.warn(`${coinId} için fiyat verisi yok, işlem oluşturulamadı`);
        return null;
    }

    // Balina işlemi için minimum tutarlar (USD)
    const minUsdAmount = WHALE_THRESHOLDS[coinId];
    const maxUsdAmount = minUsdAmount * 15; // Daha büyük işlemler için

    // Gerçek piyasa hacmine dayalı işlem boyutu
    const marketVolume = coinInfo.volume24h || minUsdAmount * 100;
    const maxRealisticAmount = Math.min(maxUsdAmount, marketVolume * 0.05); // Günlük hacmin %5'i

    const usdValue = Math.random() * (maxRealisticAmount - minUsdAmount) + minUsdAmount;
    const amount = usdValue / coinInfo.price;

    const types = ['buy', 'sell'];
    const type = types[Math.floor(Math.random() * types.length)];

    // Büyük işlemler daha çok onaylanmış olur
    const confirmationRate = usdValue > 5000000 ? 0.9 : 0.8;
    const status = Math.random() < confirmationRate ? 'confirmed' : 'pending';

    // Gerçekçi zaman damgası (son 2 saat içinde ağırlıklı)
    const timeWeight = Math.random();
    let timeOffset;
    if (timeWeight < 0.6) {
        // %60 son 1 saat
        timeOffset = Math.random() * 3600000;
    } else if (timeWeight < 0.8) {
        // %20 1-6 saat arası
        timeOffset = 3600000 + Math.random() * 18000000;
    } else {
        // %20 6-24 saat arası
        timeOffset = 21600000 + Math.random() * 64800000;
    }

    return {
        id: generateTxHash(),
        timestamp: Date.now() - timeOffset,
        coinId: coinId,
        coin: coinInfo.symbol,
        amount: amount,
        usdValue: usdValue,
        type: type,
        status: status,
        hash: generateTxHash(),
        priceAtTime: coinInfo.price * (0.95 + Math.random() * 0.1) // Küçük fiyat varyasyonu
    };
}

// Yeni işlem oluştur
function generateNewTransaction() {
    const newTx = generateRandomTransaction();

    if (newTx) { // Null kontrolü
        newTx.timestamp = Date.now(); // Şu anki zaman
        whaleTransactions.unshift(newTx);

        // Büyük işlem bildirimi
        if (newTx.usdValue > 10000000) {
            showWhaleAlert(newTx);
        }

        // En fazla 100 işlem tut
        if (whaleTransactions.length > 100) {
            whaleTransactions = whaleTransactions.slice(0, 100);
        }

        console.log(`Yeni balina işlemi: ${newTx.coin} - $${formatNumber(newTx.usdValue)}`);
    }
}

// Balina uyarısı göster
function showWhaleAlert(transaction) {
    const alertDiv = document.createElement('div');
    alertDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        color: white;
        padding: 1rem;
        border-radius: 12px;
        z-index: 1000;
        max-width: 350px;
        box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: slideIn 0.5s ease-out;
    `;

    alertDiv.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
            <i class="fas fa-whale" style="font-size: 1.5rem;"></i>
            <strong>🚨 MEGA BALINA TESPİT EDİLDİ!</strong>
        </div>
        <div style="font-size: 0.9rem;">
            <strong>${transaction.coin}</strong> - ${transaction.type === 'buy' ? 'ALIŞ' : 'SATIŞ'}<br>
            <strong>$${formatNumber(transaction.usdValue)}</strong><br>
            <small>${formatTime(transaction.timestamp)}</small>
        </div>
    `;

    document.body.appendChild(alertDiv);

    // 8 saniye sonra kaldır
    setTimeout(() => {
        alertDiv.style.animation = 'slideOut 0.5s ease-in';
        setTimeout(() => alertDiv.remove(), 500);
    }, 8000);
}

// CSS animasyonları ekle
if (!document.getElementById('whaleAlertStyles')) {
    const style = document.createElement('style');
    style.id = 'whaleAlertStyles';
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
}

// İşlem hash'i oluştur
function generateTxHash() {
    const chars = '0123456789abcdef';
    let hash = '0x';
    for (let i = 0; i < 64; i++) {
        hash += chars[Math.floor(Math.random() * chars.length)];
    }
    return hash;
}

// İstatistikleri güncelle
function updateStatistics() {
    const now = Date.now();
    const last24h = now - 86400000;

    const recent24h = whaleTransactions.filter(tx => tx.timestamp > last24h);
    const totalWhales = recent24h.length;
    const totalVolume = recent24h.reduce((sum, tx) => sum + tx.usdValue, 0);
    const largestTx = Math.max(...recent24h.map(tx => tx.usdValue));

    document.getElementById('totalWhales').textContent = totalWhales.toLocaleString();
    document.getElementById('totalVolume').textContent = '$' + formatNumber(totalVolume);
    document.getElementById('largestTx').textContent = '$' + formatNumber(largestTx);
    document.getElementById('last24h').textContent = totalWhales.toLocaleString();
}

// Sayı formatlama
function formatNumber(num) {
    if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
    if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
    if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
    return num.toFixed(2);
}

// Grafik oluştur
function createChart() {
    const ctx = document.getElementById('whaleChart').getContext('2d');

    // Son 24 saatlik veriyi saatlik gruplara böl
    const hourlyData = getHourlyData();

    chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: hourlyData.labels,
            datasets: [{
                label: 'Balina İşlem Hacmi (USD)',
                data: hourlyData.values,
                borderColor: '#00d4ff',
                backgroundColor: 'rgba(0, 212, 255, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    labels: {
                        color: '#ffffff'
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: '#cccccc'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                y: {
                    ticks: {
                        color: '#cccccc',
                        callback: function(value) {
                            return '$' + formatNumber(value);
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            }
        }
    });
}

// Saatlik veri hazırla
function getHourlyData() {
    const now = Date.now();
    const hours = [];
    const values = [];

    for (let i = 23; i >= 0; i--) {
        const hourStart = now - (i * 3600000);
        const hourEnd = hourStart + 3600000;

        const hourTxs = whaleTransactions.filter(tx =>
            tx.timestamp >= hourStart && tx.timestamp < hourEnd
        );

        const hourVolume = hourTxs.reduce((sum, tx) => sum + tx.usdValue, 0);

        const date = new Date(hourStart);
        hours.push(date.getHours() + ':00');
        values.push(hourVolume);
    }

    return { labels: hours, values: values };
}

// Grafik güncelle
function updateChart() {
    if (chart) {
        const hourlyData = getHourlyData();
        chart.data.labels = hourlyData.labels;
        chart.data.datasets[0].data = hourlyData.values;
        chart.update('none');
    }
}

// İşlem tablosunu güncelle
function updateTransactionTable() {
    const tbody = document.getElementById('whaleTableBody');
    const filteredTxs = getFilteredTransactions();

    tbody.innerHTML = '';

    filteredTxs.slice(0, 20).forEach(tx => {
        // Güvenli veri erişimi
        const coinInfo = tx.coinId ? cryptoData[tx.coinId] : null;
        const coinName = coinInfo ? coinInfo.name : 'Bilinmeyen';
        const coinSymbol = tx.coin || 'N/A';
        const displaySymbol = coinInfo ? coinInfo.displaySymbol : coinSymbol;

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${formatTime(tx.timestamp)}</td>
            <td>
                <strong>${coinSymbol}</strong><br>
                <small>${coinName}</small>
            </td>
            <td>${formatNumber(tx.amount)} ${displaySymbol}</td>
            <td>$${formatNumber(tx.usdValue)}</td>
            <td>
                <code style="font-size: 0.8rem;">
                    ${tx.hash.substring(0, 10)}...${tx.hash.substring(tx.hash.length - 8)}
                </code>
            </td>
            <td>
                <span class="transaction-type ${tx.type}">
                    ${tx.type === 'buy' ? 'ALIŞ' : 'SATIŞ'}
                </span>
            </td>
            <td>
                <span class="transaction-status ${tx.status}">
                    ${tx.status === 'confirmed' ? 'Onaylandı' : 'Beklemede'}
                </span>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Filtrelenmiş işlemleri getir
function getFilteredTransactions() {
    const coinFilter = document.getElementById('coinFilter').value;
    const minAmount = parseFloat(document.getElementById('minAmount').value) || 0;
    const timeFilter = document.getElementById('timeFilter').value;

    let filtered = whaleTransactions;

    // Coin filtresi (symbol ile karşılaştır)
    if (coinFilter !== 'all') {
        filtered = filtered.filter(tx => tx.coin === coinFilter);
    }

    // Minimum tutar filtresi
    if (minAmount > 0) {
        filtered = filtered.filter(tx => tx.usdValue >= minAmount);
    }

    // Zaman filtresi
    const now = Date.now();
    let timeLimit;
    switch (timeFilter) {
        case '1h':
            timeLimit = now - 3600000;
            break;
        case '24h':
            timeLimit = now - 86400000;
            break;
        case '7d':
            timeLimit = now - 604800000;
            break;
        case '30d':
            timeLimit = now - 2592000000;
            break;
        default:
            timeLimit = 0;
    }

    filtered = filtered.filter(tx => tx.timestamp >= timeLimit);

    return filtered;
}

// Zaman formatlama
function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) return 'Az önce';
    if (diff < 3600000) return Math.floor(diff / 60000) + ' dk önce';
    if (diff < 86400000) return Math.floor(diff / 3600000) + ' sa önce';

    return date.toLocaleDateString('tr-TR') + ' ' + date.toLocaleTimeString('tr-TR', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Uyarıları güncelle
function updateAlerts() {
    const container = document.getElementById('alertsContainer');
    const recentLarge = whaleTransactions
        .filter(tx => tx.timestamp > Date.now() - 3600000 && tx.usdValue > 5000000)
        .slice(0, 5);

    container.innerHTML = '';

    recentLarge.forEach(tx => {
        const alert = document.createElement('div');
        const severity = tx.usdValue > 10000000 ? 'high' : 'medium';

        // Güvenli veri erişimi
        const coinInfo = tx.coinId ? cryptoData[tx.coinId] : null;
        const coinSymbol = tx.coin || 'N/A';
        const displaySymbol = coinInfo ? coinInfo.displaySymbol : coinSymbol;

        alert.className = `alert ${severity}`;
        alert.innerHTML = `
            <div class="alert-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="alert-content">
                <strong>Büyük ${tx.type === 'buy' ? 'Alış' : 'Satış'} İşlemi Tespit Edildi!</strong><br>
                ${formatNumber(tx.amount)} ${displaySymbol} (${coinSymbol}) -
                $${formatNumber(tx.usdValue)} - ${formatTime(tx.timestamp)}
            </div>
        `;

        container.appendChild(alert);
    });

    if (recentLarge.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #cccccc;">Son 1 saatte büyük işlem tespit edilmedi.</p>';
    }
}

// Filtreleri uygula
function applyFilters() {
    updateTransactionTable();
}

// Piyasa analizi güncelle
function updateMarketAnalysis() {
    const selectedCoin = document.getElementById('analysisCoinFilter').value;
    updateCoinAnalysisInternal(selectedCoin);
}

// Coin analizi güncelle (dropdown değişikliğinde)
function updateCoinAnalysis() {
    const selectedCoin = document.getElementById('analysisCoinFilter').value;
    updateCoinAnalysisInternal(selectedCoin);
}

// İç analiz fonksiyonu
function updateCoinAnalysisInternal(selectedCoin) {
    try {
        let filteredTransactions = whaleTransactions.filter(tx =>
            tx.timestamp > Date.now() - 3600000 // Son 1 saat
        );

        // Coin filtresi uygula
        if (selectedCoin !== 'all') {
            filteredTransactions = filteredTransactions.filter(tx => tx.coin === selectedCoin);
        }

        // Başlıkları güncelle
        updateAnalysisTitles(selectedCoin);

        if (filteredTransactions.length === 0) {
            setDefaultAnalysis(selectedCoin);
            return;
        }

        // Alış/Satış oranını hesapla
        const buyTransactions = filteredTransactions.filter(tx => tx.type === 'buy');
        const sellTransactions = filteredTransactions.filter(tx => tx.type === 'sell');

        const buyPercentage = Math.round((buyTransactions.length / filteredTransactions.length) * 100);
        const sellPercentage = 100 - buyPercentage;

        // Hacim bazlı analiz
        const buyVolume = buyTransactions.reduce((sum, tx) => sum + tx.usdValue, 0);
        const sellVolume = sellTransactions.reduce((sum, tx) => sum + tx.usdValue, 0);
        const totalVolume = buyVolume + sellVolume;

        const buyVolumePercentage = totalVolume > 0 ? Math.round((buyVolume / totalVolume) * 100) : 50;
        const sellVolumePercentage = 100 - buyVolumePercentage;

        // Sentiment hesapla
        const sentiment = calculateMarketSentiment(buyVolumePercentage, buyPercentage, filteredTransactions);

        // UI'ı güncelle
        updateBuySellRatio(buyPercentage, sellPercentage);
        updateOverallSentiment(sentiment, selectedCoin);
        updatePricePrediction(sentiment, buyVolumePercentage, selectedCoin);
        updateTopActivity(filteredTransactions, selectedCoin);
        updateDetailedAnalysis(sentiment, buyVolumePercentage, sellVolumePercentage, filteredTransactions, selectedCoin);

    } catch (error) {
        console.error('Piyasa analizi hatası:', error);
        setDefaultAnalysis(selectedCoin);
    }
}

// Analiz başlıklarını güncelle
function updateAnalysisTitles(selectedCoin) {
    const coinName = selectedCoin === 'all' ? 'Genel Piyasa' : selectedCoin;

    document.getElementById('sentimentTitle').textContent =
        selectedCoin === 'all' ? 'Genel Piyasa Durumu' : `${coinName} Durumu`;

    document.getElementById('ratioTitle').textContent =
        selectedCoin === 'all' ? 'Alış/Satış Oranı' : `${coinName} Alış/Satış`;

    document.getElementById('predictionTitle').textContent =
        selectedCoin === 'all' ? 'Fiyat Tahmini' : `${coinName} Tahmini`;

    document.getElementById('detailedAnalysisTitle').textContent =
        selectedCoin === 'all' ? '📊 Detaylı Analiz' : `📊 ${coinName} Analizi`;
}

// Market sentiment hesapla
function calculateMarketSentiment(buyVolumePercentage, buyCountPercentage, transactions) {
    // Hacim ve işlem sayısı ağırlıklı hesaplama
    const volumeWeight = 0.7; // Hacim daha önemli
    const countWeight = 0.3;  // İşlem sayısı daha az önemli

    const weightedBuyScore = (buyVolumePercentage * volumeWeight) + (buyCountPercentage * countWeight);

    // Büyük işlemlerin etkisini hesapla
    const largeTransactions = transactions.filter(tx => tx.usdValue > 1000000);
    const largeBuyTransactions = largeTransactions.filter(tx => tx.type === 'buy');

    let largeTxBonus = 0;
    if (largeTransactions.length > 0) {
        const largeBuyRatio = largeBuyTransactions.length / largeTransactions.length;
        largeTxBonus = (largeBuyRatio - 0.5) * 20; // -10 ile +10 arası bonus
    }

    const finalScore = weightedBuyScore + largeTxBonus;

    if (finalScore >= 65) return 'bullish';
    if (finalScore <= 35) return 'bearish';
    return 'neutral';
}

// Alış/Satış oranını güncelle
function updateBuySellRatio(buyPercentage, sellPercentage) {
    document.getElementById('buyPercentage').textContent = buyPercentage + '%';
    document.getElementById('sellPercentage').textContent = sellPercentage + '%';

    // Bar animasyonları
    const buyBar = document.getElementById('buyBar');
    const sellBar = document.getElementById('sellBar');

    buyBar.style.setProperty('--width', buyPercentage + '%');
    sellBar.style.setProperty('--width', sellPercentage + '%');

    // CSS'e width değişkeni ekle
    buyBar.style.width = buyPercentage + '%';
    sellBar.style.width = sellPercentage + '%';
}

// Genel sentiment güncelle
function updateOverallSentiment(sentiment, selectedCoin = 'all') {
    const sentimentElement = document.getElementById('overallSentiment');
    const valueElement = sentimentElement.querySelector('.sentiment-value');
    const descElement = sentimentElement.querySelector('.sentiment-description');

    // Önceki class'ları temizle
    valueElement.className = 'sentiment-value';

    const coinText = selectedCoin === 'all' ? 'Balinalar' : `${selectedCoin} balinalar`;

    switch (sentiment) {
        case 'bullish':
            valueElement.classList.add('bullish');
            valueElement.textContent = '🚀 YÜKSELIŞ';
            descElement.textContent = `${coinText} alış yapıyor, fiyat yükselişe geçebilir`;
            break;
        case 'bearish':
            valueElement.classList.add('bearish');
            valueElement.textContent = '📉 DÜŞÜŞ';
            descElement.textContent = `${coinText} satış yapıyor, fiyat düşebilir`;
            break;
        default:
            valueElement.classList.add('neutral');
            valueElement.textContent = '⚖️ KARARSIZ';
            descElement.textContent = `${coinText} alış-satış dengede, yön belirsiz`;
    }
}

// Fiyat tahmini güncelle
function updatePricePrediction(sentiment, buyVolumePercentage, selectedCoin = 'all') {
    const predictionElement = document.getElementById('pricePrediction');
    const directionElement = predictionElement.querySelector('.prediction-direction');
    const confidenceElement = predictionElement.querySelector('.prediction-confidence');

    // Önceki class'ları temizle
    directionElement.className = 'prediction-direction';

    let confidence = 0;
    let icon = 'fas fa-arrow-right';
    let text = 'Yatay';
    let direction = 'sideways';

    if (sentiment === 'bullish') {
        confidence = Math.min(85, 60 + (buyVolumePercentage - 50) * 0.5);
        icon = 'fas fa-arrow-up';
        text = selectedCoin === 'all' ? 'Genel Yükseliş' : `${selectedCoin} Yükseliş`;
        direction = 'up';
    } else if (sentiment === 'bearish') {
        confidence = Math.min(85, 60 + (50 - buyVolumePercentage) * 0.5);
        icon = 'fas fa-arrow-down';
        text = selectedCoin === 'all' ? 'Genel Düşüş' : `${selectedCoin} Düşüş`;
        direction = 'down';
    } else {
        confidence = 45 + Math.random() * 20; // 45-65 arası
        text = selectedCoin === 'all' ? 'Yatay Seyir' : `${selectedCoin} Yatay`;
    }

    directionElement.classList.add(direction);
    directionElement.innerHTML = `<i class="${icon}"></i><span>${text}</span>`;
    confidenceElement.textContent = `Güven: ${Math.round(confidence)}%`;
}

// En aktif coin güncelle
function updateTopActivity(transactions, selectedCoin = 'all') {
    const activityElement = document.getElementById('topActivity');
    const coinNameElement = activityElement.querySelector('.coin-name');
    const countElement = activityElement.querySelector('.activity-count');
    const trendElement = activityElement.querySelector('.activity-trend');

    if (selectedCoin !== 'all') {
        // Belirli bir coin seçilmişse, o coin'in aktivitesini göster
        const coinTransactions = transactions.filter(tx => tx.coin === selectedCoin);

        if (coinTransactions.length === 0) {
            coinNameElement.textContent = selectedCoin;
            countElement.textContent = '0 işlem';
            trendElement.textContent = 'Veri Yok';
            return;
        }

        // Trend hesapla (son 30 dakika vs önceki 30 dakika)
        const now = Date.now();
        const recent30min = coinTransactions.filter(tx =>
            tx.timestamp > now - 1800000
        ).length;
        const previous30min = whaleTransactions.filter(tx =>
            tx.timestamp <= now - 1800000 &&
            tx.timestamp > now - 3600000 &&
            tx.coin === selectedCoin
        ).length;

        let trend = 'Stabil';
        if (recent30min > previous30min * 1.5) trend = '🔥 Artan';
        else if (recent30min < previous30min * 0.5) trend = '❄️ Azalan';

        coinNameElement.textContent = selectedCoin;
        countElement.textContent = `${coinTransactions.length} işlem`;
        trendElement.textContent = trend;

    } else {
        // Genel piyasa - en aktif coin'i bul
        const coinCounts = {};
        transactions.forEach(tx => {
            const coin = tx.coin || 'Unknown';
            coinCounts[coin] = (coinCounts[coin] || 0) + 1;
        });

        if (Object.keys(coinCounts).length === 0) {
            coinNameElement.textContent = '-';
            countElement.textContent = '0 işlem';
            trendElement.textContent = '-';
            return;
        }

        // En aktif coin'i bul
        const topCoin = Object.entries(coinCounts)
            .sort(([,a], [,b]) => b - a)[0];

        const [coinSymbol, count] = topCoin;

        // Trend hesapla
        const now = Date.now();
        const recent30min = transactions.filter(tx =>
            tx.timestamp > now - 1800000 && tx.coin === coinSymbol
        ).length;
        const previous30min = whaleTransactions.filter(tx =>
            tx.timestamp <= now - 1800000 &&
            tx.timestamp > now - 3600000 &&
            tx.coin === coinSymbol
        ).length;

        let trend = 'Stabil';
        if (recent30min > previous30min * 1.5) trend = '🔥 Artan';
        else if (recent30min < previous30min * 0.5) trend = '❄️ Azalan';

        coinNameElement.textContent = coinSymbol;
        countElement.textContent = `${count} işlem`;
        trendElement.textContent = trend;
    }
}

// Detaylı analiz güncelle
function updateDetailedAnalysis(sentiment, buyVolumePercentage, sellVolumePercentage, transactions, selectedCoin = 'all') {
    const analysisElement = document.getElementById('analysisText');

    const totalVolume = transactions.reduce((sum, tx) => sum + tx.usdValue, 0);
    const avgTransactionSize = transactions.length > 0 ? totalVolume / transactions.length : 0;
    const largeTransactions = transactions.filter(tx => tx.usdValue > 1000000);

    const coinText = selectedCoin === 'all' ? 'Genel Piyasa' : selectedCoin;
    const whaleText = selectedCoin === 'all' ? 'Balinalar' : `${selectedCoin} balinalar`;

    let analysis = `📊 <strong>${coinText} - Son 1 Saat Analizi:</strong><br><br>`;

    if (transactions.length === 0) {
        analysis += `⏳ <strong>Veri Durumu:</strong> ${coinText} için son 1 saatte balina işlemi tespit edilmedi.<br><br>`;
        analysis += `💡 <strong>Öneri:</strong> Daha uzun zaman aralığını kontrol edin veya farklı bir coin seçin.`;
        analysisElement.innerHTML = analysis;
        return;
    }

    analysis += `• <strong>Toplam İşlem:</strong> ${transactions.length} adet<br>`;
    analysis += `• <strong>Toplam Hacim:</strong> $${formatNumber(totalVolume)}<br>`;
    analysis += `• <strong>Ortalama İşlem:</strong> $${formatNumber(avgTransactionSize)}<br>`;
    analysis += `• <strong>Büyük İşlemler:</strong> ${largeTransactions.length} adet (>1M USD)<br><br>`;

    analysis += `💰 <strong>Hacim Dağılımı:</strong><br>`;
    analysis += `• Alış Hacmi: %${buyVolumePercentage}<br>`;
    analysis += `• Satış Hacmi: %${sellVolumePercentage}<br><br>`;

    // Coin özel bilgiler
    if (selectedCoin !== 'all') {
        const coinInfo = Object.values(cryptoData).find(coin => coin.symbol === selectedCoin);
        if (coinInfo && coinInfo.price > 0) {
            analysis += `💎 <strong>${selectedCoin} Bilgileri:</strong><br>`;
            analysis += `• Güncel Fiyat: $${coinInfo.price.toLocaleString()}<br>`;
            analysis += `• 24s Değişim: %${coinInfo.change24h?.toFixed(2) || 'N/A'}<br>`;
            analysis += `• Piyasa Değeri: $${formatNumber(coinInfo.marketCap || 0)}<br><br>`;
        }
    }

    // Sentiment bazlı yorum
    if (sentiment === 'bullish') {
        analysis += `🚀 <strong>Yorum:</strong> ${whaleText} güçlü alış yapıyor. `;
        if (buyVolumePercentage > 70) {
            analysis += `Hacim ağırlıklı alış baskısı çok yüksek. ${selectedCoin === 'all' ? 'Genel piyasa' : selectedCoin} fiyat yükselişi muhtemel.`;
        } else {
            analysis += `Orta seviye alış baskısı var. Dikkatli yükseliş beklenebilir.`;
        }
    } else if (sentiment === 'bearish') {
        analysis += `📉 <strong>Yorum:</strong> ${whaleText} satış yapıyor. `;
        if (sellVolumePercentage > 70) {
            analysis += `Güçlü satış baskısı var. ${selectedCoin === 'all' ? 'Genel piyasa' : selectedCoin} fiyat düşüşü muhtemel.`;
        } else {
            analysis += `Orta seviye satış baskısı. Dikkatli düşüş beklenebilir.`;
        }
    } else {
        analysis += `⚖️ <strong>Yorum:</strong> ${coinText} kararsız. Alış-satış dengede. `;
        analysis += `Büyük bir hareket için daha fazla veri gerekli.`;
    }

    if (largeTransactions.length > 0) {
        analysis += `<br><br>⚠️ <strong>Dikkat:</strong> ${largeTransactions.length} adet mega işlem tespit edildi. Bu büyük oyuncuların ${selectedCoin === 'all' ? 'piyasada' : selectedCoin + ' üzerinde'} aktif olduğunu gösteriyor.`;
    }

    // Öneriler
    analysis += `<br><br>💡 <strong>Öneriler:</strong><br>`;
    if (sentiment === 'bullish') {
        analysis += `• Alış fırsatları değerlendirilebilir<br>`;
        analysis += `• Stop-loss seviyelerini güncelleyin<br>`;
        analysis += `• Hacim artışını takip edin`;
    } else if (sentiment === 'bearish') {
        analysis += `• Pozisyon boyutlarını gözden geçirin<br>`;
        analysis += `• Satış seviyelerini belirleyin<br>`;
        analysis += `• Risk yönetimi uygulayın`;
    } else {
        analysis += `• Bekle ve gözle stratejisi uygulayın<br>`;
        analysis += `• Daha fazla veri bekleyin<br>`;
        analysis += `• Diğer coinleri de kontrol edin`;
    }

    analysisElement.innerHTML = analysis;
}

// Varsayılan analiz ayarla
function setDefaultAnalysis(selectedCoin = 'all') {
    document.getElementById('buyPercentage').textContent = '50%';
    document.getElementById('sellPercentage').textContent = '50%';

    const buyBar = document.getElementById('buyBar');
    const sellBar = document.getElementById('sellBar');
    buyBar.style.width = '50%';
    sellBar.style.width = '50%';

    const coinText = selectedCoin === 'all' ? 'Genel piyasa' : selectedCoin;

    const sentimentElement = document.getElementById('overallSentiment');
    sentimentElement.querySelector('.sentiment-value').textContent = 'Veri Bekleniyor';
    sentimentElement.querySelector('.sentiment-description').textContent = `${coinText} için yeterli işlem verisi yok`;

    const predictionElement = document.getElementById('pricePrediction');
    predictionElement.querySelector('.prediction-direction').innerHTML = '<i class="fas fa-clock"></i><span>Beklemede</span>';
    predictionElement.querySelector('.prediction-confidence').textContent = 'Güven: 0%';

    const activityElement = document.getElementById('topActivity');
    if (selectedCoin === 'all') {
        activityElement.querySelector('.coin-name').textContent = '-';
        activityElement.querySelector('.activity-count').textContent = '0 işlem';
        activityElement.querySelector('.activity-trend').textContent = '-';
    } else {
        activityElement.querySelector('.coin-name').textContent = selectedCoin;
        activityElement.querySelector('.activity-count').textContent = '0 işlem';
        activityElement.querySelector('.activity-trend').textContent = 'Veri Yok';
    }

    document.getElementById('analysisText').innerHTML =
        `${coinText} için balina işlemleri bekleniyor. Analiz için yeterli veri toplanıyor...<br><br>` +
        `💡 <strong>İpucu:</strong> Farklı bir coin seçmeyi veya daha uzun süre beklemeyi deneyin.`;
}

// Sayfa kapatılırken interval'ları temizle
window.addEventListener('beforeunload', function() {
    if (updateInterval) {
        clearInterval(updateInterval);
    }
    if (priceUpdateInterval) {
        clearInterval(priceUpdateInterval);
    }
});

// Gerçek zamanlı durum göstergesi güncelle
function updateConnectionStatus() {
    const statusDot = document.querySelector('.status-dot');
    const statusText = statusDot?.parentElement;

    if (statusDot && statusText) {
        // API bağlantısını kontrol et
        const hasRecentData = Object.values(cryptoData).some(coin => coin.price > 0);

        if (hasRecentData) {
            statusDot.style.background = '#00ff00';
            statusText.innerHTML = '<span class="status-dot"></span>Canlı - Gerçek Veriler';
            statusText.style.color = '#00ff00';
        } else {
            statusDot.style.background = '#ffa500';
            statusText.innerHTML = '<span class="status-dot"></span>Bağlantı Kuruluyor...';
            statusText.style.color = '#ffa500';
        }
    }
}

// Sayfa görünürlük değişikliklerini izle
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // Sayfa gizlendiğinde güncelleme sıklığını azalt
        if (updateInterval) {
            clearInterval(updateInterval);
            updateInterval = setInterval(() => {
                generateNewTransaction();
                updateStatistics();
                updateChart();
                updateTransactionTable();
                updateAlerts();
            }, 30000); // 30 saniyede bir
        }
    } else {
        // Sayfa tekrar görünür olduğunda normal sıklığa dön
        if (updateInterval) {
            clearInterval(updateInterval);
            updateInterval = setInterval(() => {
                generateNewTransaction();
                updateStatistics();
                updateChart();
                updateTransactionTable();
                updateAlerts();
            }, 10000); // 10 saniyede bir
        }

        // Hemen bir güncelleme yap
        generateNewTransaction();
        updateStatistics();
        updateChart();
        updateTransactionTable();
        updateAlerts();
    }
});

// Bağlantı durumunu periyodik olarak kontrol et
setInterval(updateConnectionStatus, 5000);

// Navigation Functions
function showSection(sectionName) {
    // Tüm bölümleri gizle
    const sections = ['dashboard', 'portfolio', 'analytics', 'reports'];
    sections.forEach(section => {
        const element = document.querySelector(`.${section}-section, #${section}Section`);
        if (element) {
            element.style.display = 'none';
        }
    });

    // Ana bölümleri kontrol et
    const mainSections = document.querySelectorAll('.stats-section, .filters-section, .chart-section, .transactions-section, .market-analysis-section, .alerts-section');

    if (sectionName === 'dashboard') {
        mainSections.forEach(section => section.style.display = 'block');
    } else {
        mainSections.forEach(section => section.style.display = 'none');

        // Seçilen bölümü göster
        const targetSection = document.querySelector(`.${sectionName}-section, #${sectionName}Section`);
        if (targetSection) {
            targetSection.style.display = 'block';
        }
    }

    // Nav linklerini güncelle
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });

    const activeLink = document.querySelector(`[href="#${sectionName}"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }

    currentSection = sectionName;

    // Bölüm özel güncellemeler
    if (sectionName === 'portfolio') {
        updatePortfolio();
    } else if (sectionName === 'analytics') {
        updateTechnicalAnalysis();
    }
}

// Navigation event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Nav link event listeners
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const href = this.getAttribute('href');
            const sectionName = href.replace('#', '');
            showSection(sectionName);
        });
    });
});

// Theme Toggle
function toggleTheme() {
    const body = document.body;
    const themeIcon = document.getElementById('themeIcon');

    if (body.getAttribute('data-theme') === 'light') {
        body.removeAttribute('data-theme');
        themeIcon.className = 'fas fa-moon';
        localStorage.setItem('theme', 'dark');
    } else {
        body.setAttribute('data-theme', 'light');
        themeIcon.className = 'fas fa-sun';
        localStorage.setItem('theme', 'light');
    }
}

// Load saved theme
function loadTheme() {
    const savedTheme = localStorage.getItem('theme');
    const themeIcon = document.getElementById('themeIcon');

    if (savedTheme === 'light') {
        document.body.setAttribute('data-theme', 'light');
        if (themeIcon) themeIcon.className = 'fas fa-sun';
    } else {
        if (themeIcon) themeIcon.className = 'fas fa-moon';
    }
}

// Fullscreen Toggle
function toggleFullscreen() {
    const fullscreenIcon = document.getElementById('fullscreenIcon');

    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().then(() => {
            fullscreenIcon.className = 'fas fa-compress';
            document.body.classList.add('fullscreen-mode');
        });
    } else {
        document.exitFullscreen().then(() => {
            fullscreenIcon.className = 'fas fa-expand';
            document.body.classList.remove('fullscreen-mode');
        });
    }
}

// Portfolio Management
function loadPortfolio() {
    const savedPortfolio = localStorage.getItem('cryptoPortfolio');
    if (savedPortfolio) {
        portfolio = JSON.parse(savedPortfolio);
    }
}

function savePortfolio() {
    localStorage.setItem('cryptoPortfolio', JSON.stringify(portfolio));
}

function showAddCoinModal() {
    document.getElementById('addCoinModal').style.display = 'block';
}

function closeAddCoinModal() {
    document.getElementById('addCoinModal').style.display = 'none';
    document.getElementById('addCoinForm').reset();
}

// Add coin form handler
document.addEventListener('DOMContentLoaded', function() {
    const addCoinForm = document.getElementById('addCoinForm');
    if (addCoinForm) {
        addCoinForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const coinSymbol = document.getElementById('modalCoinSelect').value;
            const amount = parseFloat(document.getElementById('coinAmount').value);
            const buyPrice = parseFloat(document.getElementById('buyPrice').value);

            if (coinSymbol && amount > 0 && buyPrice > 0) {
                addCoinToPortfolio(coinSymbol, amount, buyPrice);
                closeAddCoinModal();
                updatePortfolio();
            }
        });
    }
});

function addCoinToPortfolio(symbol, amount, buyPrice) {
    const existingCoin = portfolio.find(coin => coin.symbol === symbol);

    if (existingCoin) {
        // Ortalama fiyat hesapla
        const totalValue = (existingCoin.amount * existingCoin.avgPrice) + (amount * buyPrice);
        const totalAmount = existingCoin.amount + amount;
        existingCoin.avgPrice = totalValue / totalAmount;
        existingCoin.amount = totalAmount;
    } else {
        portfolio.push({
            symbol: symbol,
            amount: amount,
            avgPrice: buyPrice,
            addedDate: Date.now()
        });
    }

    savePortfolio();
}

function removeCoinFromPortfolio(symbol) {
    portfolio = portfolio.filter(coin => coin.symbol !== symbol);
    savePortfolio();
    updatePortfolio();
}

function updatePortfolio() {
    if (currentSection !== 'portfolio') return;

    const tbody = document.getElementById('portfolioTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    let totalValue = 0;
    let totalCost = 0;
    let bestPerformer = null;
    let bestPerformance = -Infinity;

    portfolio.forEach(coin => {
        const coinData = Object.values(cryptoData).find(c => c.symbol === coin.symbol);
        if (!coinData) return;

        const currentPrice = coinData.price || 0;
        const currentValue = coin.amount * currentPrice;
        const cost = coin.amount * coin.avgPrice;
        const pnl = currentValue - cost;
        const pnlPercentage = cost > 0 ? ((pnl / cost) * 100) : 0;

        totalValue += currentValue;
        totalCost += cost;

        if (pnlPercentage > bestPerformance) {
            bestPerformance = pnlPercentage;
            bestPerformer = coin.symbol;
        }

        const row = document.createElement('tr');
        row.innerHTML = `
            <td><strong>${coin.symbol}</strong></td>
            <td>${coin.amount.toFixed(8)}</td>
            <td>$${coin.avgPrice.toFixed(2)}</td>
            <td>$${currentPrice.toFixed(2)}</td>
            <td>$${formatNumber(currentValue)}</td>
            <td class="${pnl >= 0 ? 'positive' : 'negative'}">$${formatNumber(Math.abs(pnl))}</td>
            <td class="${pnlPercentage >= 0 ? 'positive' : 'negative'}">${pnlPercentage >= 0 ? '+' : ''}${pnlPercentage.toFixed(2)}%</td>
            <td>
                <button onclick="removeCoinFromPortfolio('${coin.symbol}')" style="background: var(--danger); color: white; border: none; padding: 0.25rem 0.5rem; border-radius: 4px; cursor: pointer;">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });

    // Portfolio summary güncelle
    const totalPnL = totalValue - totalCost;
    const totalPnLPercentage = totalCost > 0 ? ((totalPnL / totalCost) * 100) : 0;

    document.getElementById('totalPortfolioValue').textContent = '$' + formatNumber(totalValue);
    document.getElementById('dailyPnL').textContent = '$' + formatNumber(Math.abs(totalPnL));
    document.getElementById('dailyPnLPercentage').textContent = (totalPnL >= 0 ? '+' : '') + totalPnLPercentage.toFixed(2) + '%';

    // Change classes
    const changeElement = document.getElementById('totalPortfolioChange');
    const pnlElement = document.getElementById('dailyPnL');
    const pnlPercentageElement = document.getElementById('dailyPnLPercentage');

    changeElement.className = `portfolio-change ${totalPnL >= 0 ? 'positive' : 'negative'}`;
    pnlElement.className = `portfolio-pnl ${totalPnL >= 0 ? 'positive' : 'negative'}`;
    pnlPercentageElement.className = `pnl-percentage ${totalPnL >= 0 ? 'positive' : 'negative'}`;

    // Best performer
    if (bestPerformer) {
        document.getElementById('bestPerformer').textContent = bestPerformer;
        document.getElementById('bestPerformerChange').textContent = '+' + bestPerformance.toFixed(2) + '%';
        document.getElementById('bestPerformerChange').className = 'performer-change positive';
    } else {
        document.getElementById('bestPerformer').textContent = '-';
        document.getElementById('bestPerformerChange').textContent = '0%';
    }
}

// Technical Analysis
function updateTechnicalAnalysis() {
    if (currentSection !== 'analytics') return;

    const selectedCoin = document.getElementById('analysisCoinFilter')?.value || 'BTC';
    const coinData = Object.values(cryptoData).find(c => c.symbol === selectedCoin);

    if (!coinData || !coinData.price) {
        setDefaultTechnicalValues();
        return;
    }

    // Simulated technical indicators (in real app, these would be calculated from price history)
    const price = coinData.price;
    const change24h = coinData.change24h || 0;

    // RSI (Relative Strength Index)
    const rsi = 50 + (change24h * 2); // Simplified calculation
    const clampedRsi = Math.max(0, Math.min(100, rsi));

    document.getElementById('rsiValue').textContent = clampedRsi.toFixed(1);
    document.getElementById('rsiFill').style.width = clampedRsi + '%';

    // MACD (Moving Average Convergence Divergence)
    const macd = change24h * 0.1;
    const signal = macd * 0.8;
    const histogram = macd - signal;

    document.getElementById('macdValue').textContent = macd.toFixed(4);
    document.getElementById('signalValue').textContent = signal.toFixed(4);
    document.getElementById('histogramValue').textContent = histogram.toFixed(4);

    // Bollinger Bands
    const volatility = Math.abs(change24h) * 0.02;
    const upperBand = price * (1 + volatility);
    const middleBand = price;
    const lowerBand = price * (1 - volatility);

    document.getElementById('upperBand').textContent = '$' + upperBand.toFixed(2);
    document.getElementById('middleBand').textContent = '$' + middleBand.toFixed(2);
    document.getElementById('lowerBand').textContent = '$' + lowerBand.toFixed(2);

    // Support/Resistance
    const resistance1 = price * 1.05;
    const support1 = price * 0.95;

    document.getElementById('resistance1').textContent = '$' + resistance1.toFixed(2);
    document.getElementById('support1').textContent = '$' + support1.toFixed(2);
}

function setDefaultTechnicalValues() {
    document.getElementById('rsiValue').textContent = '50';
    document.getElementById('rsiFill').style.width = '50%';

    document.getElementById('macdValue').textContent = '0';
    document.getElementById('signalValue').textContent = '0';
    document.getElementById('histogramValue').textContent = '0';

    document.getElementById('upperBand').textContent = '$0';
    document.getElementById('middleBand').textContent = '$0';
    document.getElementById('lowerBand').textContent = '$0';

    document.getElementById('resistance1').textContent = '$0';
    document.getElementById('support1').textContent = '$0';
}

// Report Generation
function generateDailyReport() {
    const reportData = {
        date: new Date().toLocaleDateString('tr-TR'),
        totalTransactions: whaleTransactions.filter(tx => tx.timestamp > Date.now() - 86400000).length,
        totalVolume: whaleTransactions
            .filter(tx => tx.timestamp > Date.now() - 86400000)
            .reduce((sum, tx) => sum + tx.usdValue, 0),
        topCoins: getTopActiveCoins(24),
        sentiment: calculateOverallSentiment()
    };

    downloadReport('daily', reportData);
}

function generateWeeklyReport() {
    const reportData = {
        date: new Date().toLocaleDateString('tr-TR'),
        totalTransactions: whaleTransactions.filter(tx => tx.timestamp > Date.now() - 604800000).length,
        totalVolume: whaleTransactions
            .filter(tx => tx.timestamp > Date.now() - 604800000)
            .reduce((sum, tx) => sum + tx.usdValue, 0),
        topCoins: getTopActiveCoins(168),
        sentiment: calculateOverallSentiment()
    };

    downloadReport('weekly', reportData);
}

function generatePortfolioReport() {
    if (portfolio.length === 0) {
        alert('Portföyünüzde coin bulunmuyor!');
        return;
    }

    let totalValue = 0;
    let totalCost = 0;

    const portfolioData = portfolio.map(coin => {
        const coinData = Object.values(cryptoData).find(c => c.symbol === coin.symbol);
        const currentPrice = coinData?.price || 0;
        const currentValue = coin.amount * currentPrice;
        const cost = coin.amount * coin.avgPrice;

        totalValue += currentValue;
        totalCost += cost;

        return {
            symbol: coin.symbol,
            amount: coin.amount,
            avgPrice: coin.avgPrice,
            currentPrice: currentPrice,
            currentValue: currentValue,
            pnl: currentValue - cost,
            pnlPercentage: cost > 0 ? ((currentValue - cost) / cost * 100) : 0
        };
    });

    const reportData = {
        date: new Date().toLocaleDateString('tr-TR'),
        totalValue: totalValue,
        totalCost: totalCost,
        totalPnL: totalValue - totalCost,
        totalPnLPercentage: totalCost > 0 ? ((totalValue - totalCost) / totalCost * 100) : 0,
        coins: portfolioData
    };

    downloadReport('portfolio', reportData);
}

function getTopActiveCoins(hours) {
    const timeLimit = Date.now() - (hours * 3600000);
    const recentTxs = whaleTransactions.filter(tx => tx.timestamp > timeLimit);

    const coinCounts = {};
    recentTxs.forEach(tx => {
        coinCounts[tx.coin] = (coinCounts[tx.coin] || 0) + 1;
    });

    return Object.entries(coinCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([coin, count]) => ({ coin, count }));
}

function calculateOverallSentiment() {
    const recentTxs = whaleTransactions.filter(tx => tx.timestamp > Date.now() - 3600000);
    if (recentTxs.length === 0) return 'neutral';

    const buyTxs = recentTxs.filter(tx => tx.type === 'buy');
    const buyPercentage = (buyTxs.length / recentTxs.length) * 100;

    if (buyPercentage >= 65) return 'bullish';
    if (buyPercentage <= 35) return 'bearish';
    return 'neutral';
}

function downloadReport(type, data) {
    let content = '';

    if (type === 'daily') {
        content = `GÜNLÜK BALINA RAPORU - ${data.date}\n\n`;
        content += `Toplam İşlem: ${data.totalTransactions}\n`;
        content += `Toplam Hacim: $${formatNumber(data.totalVolume)}\n`;
        content += `Genel Sentiment: ${data.sentiment}\n\n`;
        content += `En Aktif Coinler:\n`;
        data.topCoins.forEach(coin => {
            content += `- ${coin.coin}: ${coin.count} işlem\n`;
        });
    } else if (type === 'weekly') {
        content = `HAFTALIK BALINA RAPORU - ${data.date}\n\n`;
        content += `Toplam İşlem: ${data.totalTransactions}\n`;
        content += `Toplam Hacim: $${formatNumber(data.totalVolume)}\n`;
        content += `Genel Sentiment: ${data.sentiment}\n\n`;
        content += `En Aktif Coinler:\n`;
        data.topCoins.forEach(coin => {
            content += `- ${coin.coin}: ${coin.count} işlem\n`;
        });
    } else if (type === 'portfolio') {
        content = `PORTFÖY RAPORU - ${data.date}\n\n`;
        content += `Toplam Değer: $${formatNumber(data.totalValue)}\n`;
        content += `Toplam Maliyet: $${formatNumber(data.totalCost)}\n`;
        content += `Toplam P&L: $${formatNumber(data.totalPnL)} (${data.totalPnLPercentage.toFixed(2)}%)\n\n`;
        content += `Coin Detayları:\n`;
        data.coins.forEach(coin => {
            content += `- ${coin.symbol}: ${coin.amount.toFixed(8)} adet\n`;
            content += `  Ortalama: $${coin.avgPrice.toFixed(2)} | Güncel: $${coin.currentPrice.toFixed(2)}\n`;
            content += `  P&L: $${formatNumber(coin.pnl)} (${coin.pnlPercentage.toFixed(2)}%)\n\n`;
        });
    }

    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `balina-rapor-${type}-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}