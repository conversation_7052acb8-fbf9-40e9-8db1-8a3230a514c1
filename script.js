// Kripto Balina Takip Sistemi JavaScript - Gerçek Zamanlı Veriler

// Global değişkenler
let whaleTransactions = [];
let chart;
let updateInterval;
let priceUpdateInterval;
let realTimeData = {};

// Kripto para verileri - Gerçek zamanlı güncellenecek
const cryptoData = {
    'bitcoin': {
        name: 'Bitcoin',
        symbol: 'BTC',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: '₿'
    },
    'ethereum': {
        name: 'Ethereum',
        symbol: 'ETH',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'Ξ'
    },
    'binancecoin': {
        name: 'Binance Coin',
        symbol: 'BNB',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'BNB'
    },
    'cardano': {
        name: 'Cardano',
        symbol: 'ADA',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'ADA'
    },
    'solana': {
        name: '<PERSON><PERSON>',
        symbol: 'SOL',
        price: 0,
        change24h: 0,
        volume24h: 0,
        marketCap: 0,
        displaySymbol: 'SOL'
    }
};

// API URL'leri
const COINGECKO_API = 'https://api.coingecko.com/api/v3';
const CORS_PROXY = 'https://api.allorigins.win/raw?url=';

// Balina tespiti için minimum tutarlar (USD)
const WHALE_THRESHOLDS = {
    'bitcoin': 1000000,    // 1M USD
    'ethereum': 500000,    // 500K USD
    'binancecoin': 200000, // 200K USD
    'cardano': 100000,     // 100K USD
    'solana': 150000       // 150K USD
};

// Sayfa yüklendiğinde çalışacak fonksiyon
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

async function initializeApp() {
    showLoadingMessage();

    try {
        console.log('Uygulama başlatılıyor...');

        // Gerçek fiyat verilerini al
        await fetchRealTimePrices();
        console.log('Fiyat verileri alındı:', cryptoData);

        // İlk veri setini oluştur
        generateInitialData();
        console.log('İlk veriler oluşturuldu, işlem sayısı:', whaleTransactions.length);

        updateStatistics();
        createChart();
        updateTransactionTable();
        updateAlerts();

        hideLoadingMessage();
        console.log('Uygulama başarıyla başlatıldı!');

        // Fiyatları her 30 saniyede bir güncelle
        priceUpdateInterval = setInterval(async () => {
            try {
                await fetchRealTimePrices();
                updateStatistics();
            } catch (error) {
                console.error('Fiyat güncelleme hatası:', error);
            }
        }, 30000);

        // Balina işlemlerini her 10 saniyede bir güncelle
        updateInterval = setInterval(() => {
            try {
                generateNewTransaction();
                updateStatistics();
                updateChart();
                updateTransactionTable();
                updateAlerts();
            } catch (error) {
                console.error('İşlem güncelleme hatası:', error);
            }
        }, 10000);

    } catch (error) {
        console.error('Uygulama başlatılırken hata:', error);
        hideLoadingMessage();
        showErrorMessage('Veriler yüklenirken hata oluştu. Lütfen sayfayı yenileyin.');
    }
}

// Gerçek zamanlı fiyat verilerini çek
async function fetchRealTimePrices() {
    try {
        const coinIds = Object.keys(cryptoData).join(',');
        const url = `${COINGECKO_API}/simple/price?ids=${coinIds}&vs_currencies=usd&include_24hr_change=true&include_24hr_vol=true&include_market_cap=true`;

        const response = await fetch(url);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // Veriyi güncelle
        Object.keys(cryptoData).forEach(coinId => {
            if (data[coinId]) {
                cryptoData[coinId].price = data[coinId].usd || 0;
                cryptoData[coinId].change24h = data[coinId].usd_24h_change || 0;
                cryptoData[coinId].volume24h = data[coinId].usd_24h_vol || 0;
                cryptoData[coinId].marketCap = data[coinId].usd_market_cap || 0;
            }
        });

        console.log('Fiyat verileri güncellendi:', new Date().toLocaleTimeString());

    } catch (error) {
        console.error('Fiyat verileri alınırken hata:', error);
        // Hata durumunda varsayılan değerleri kullan
        if (Object.values(cryptoData).every(coin => coin.price === 0)) {
            setDefaultPrices();
        }
    }
}

// Varsayılan fiyatları ayarla (API erişimi olmadığında)
function setDefaultPrices() {
    cryptoData.bitcoin.price = 43250;
    cryptoData.ethereum.price = 2580;
    cryptoData.binancecoin.price = 315;
    cryptoData.cardano.price = 0.48;
    cryptoData.solana.price = 98;

    console.log('Varsayılan fiyatlar kullanılıyor');
}

// Yükleme mesajı göster
function showLoadingMessage() {
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'loadingMessage';
    loadingDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.9);
        color: #00d4ff;
        padding: 2rem;
        border-radius: 10px;
        text-align: center;
        z-index: 1000;
        border: 1px solid rgba(0, 212, 255, 0.3);
    `;
    loadingDiv.innerHTML = `
        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 1rem;"></i>
        <h3>Gerçek Zamanlı Veriler Yükleniyor...</h3>
        <p>CoinGecko API'den güncel fiyatlar alınıyor</p>
    `;
    document.body.appendChild(loadingDiv);
}

// Yükleme mesajını gizle
function hideLoadingMessage() {
    const loadingDiv = document.getElementById('loadingMessage');
    if (loadingDiv) {
        loadingDiv.remove();
    }
}

// Hata mesajı göster
function showErrorMessage(message) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(255, 0, 0, 0.9);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        z-index: 1000;
        max-width: 300px;
    `;
    errorDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i> ${message}
    `;
    document.body.appendChild(errorDiv);

    setTimeout(() => errorDiv.remove(), 5000);
}

// İlk veri setini oluştur
function generateInitialData() {
    whaleTransactions = []; // Önce temizle

    for (let i = 0; i < 20; i++) {
        const newTx = generateRandomTransaction();
        if (newTx) { // Null kontrolü
            whaleTransactions.push(newTx);
        }
    }

    // Tarihe göre sırala (en yeni önce)
    whaleTransactions.sort((a, b) => b.timestamp - a.timestamp);

    console.log(`${whaleTransactions.length} adet başlangıç işlemi oluşturuldu`);
}

// Gerçek verilerle balina işlemi oluştur
function generateRandomTransaction() {
    const coinIds = Object.keys(cryptoData);
    const coinId = coinIds[Math.floor(Math.random() * coinIds.length)];
    const coinInfo = cryptoData[coinId];

    // Gerçek fiyat kontrolü
    if (!coinInfo.price || coinInfo.price === 0) {
        console.warn(`${coinId} için fiyat verisi yok, işlem oluşturulamadı`);
        return null;
    }

    // Balina işlemi için minimum tutarlar (USD)
    const minUsdAmount = WHALE_THRESHOLDS[coinId];
    const maxUsdAmount = minUsdAmount * 15; // Daha büyük işlemler için

    // Gerçek piyasa hacmine dayalı işlem boyutu
    const marketVolume = coinInfo.volume24h || minUsdAmount * 100;
    const maxRealisticAmount = Math.min(maxUsdAmount, marketVolume * 0.05); // Günlük hacmin %5'i

    const usdValue = Math.random() * (maxRealisticAmount - minUsdAmount) + minUsdAmount;
    const amount = usdValue / coinInfo.price;

    const types = ['buy', 'sell'];
    const type = types[Math.floor(Math.random() * types.length)];

    // Büyük işlemler daha çok onaylanmış olur
    const confirmationRate = usdValue > 5000000 ? 0.9 : 0.8;
    const status = Math.random() < confirmationRate ? 'confirmed' : 'pending';

    // Gerçekçi zaman damgası (son 2 saat içinde ağırlıklı)
    const timeWeight = Math.random();
    let timeOffset;
    if (timeWeight < 0.6) {
        // %60 son 1 saat
        timeOffset = Math.random() * 3600000;
    } else if (timeWeight < 0.8) {
        // %20 1-6 saat arası
        timeOffset = 3600000 + Math.random() * 18000000;
    } else {
        // %20 6-24 saat arası
        timeOffset = 21600000 + Math.random() * 64800000;
    }

    return {
        id: generateTxHash(),
        timestamp: Date.now() - timeOffset,
        coinId: coinId,
        coin: coinInfo.symbol,
        amount: amount,
        usdValue: usdValue,
        type: type,
        status: status,
        hash: generateTxHash(),
        priceAtTime: coinInfo.price * (0.95 + Math.random() * 0.1) // Küçük fiyat varyasyonu
    };
}

// Yeni işlem oluştur
function generateNewTransaction() {
    const newTx = generateRandomTransaction();

    if (newTx) { // Null kontrolü
        newTx.timestamp = Date.now(); // Şu anki zaman
        whaleTransactions.unshift(newTx);

        // Büyük işlem bildirimi
        if (newTx.usdValue > 10000000) {
            showWhaleAlert(newTx);
        }

        // En fazla 100 işlem tut
        if (whaleTransactions.length > 100) {
            whaleTransactions = whaleTransactions.slice(0, 100);
        }

        console.log(`Yeni balina işlemi: ${newTx.coin} - $${formatNumber(newTx.usdValue)}`);
    }
}

// Balina uyarısı göster
function showWhaleAlert(transaction) {
    const alertDiv = document.createElement('div');
    alertDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        color: white;
        padding: 1rem;
        border-radius: 12px;
        z-index: 1000;
        max-width: 350px;
        box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: slideIn 0.5s ease-out;
    `;

    alertDiv.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 8px;">
            <i class="fas fa-whale" style="font-size: 1.5rem;"></i>
            <strong>🚨 MEGA BALINA TESPİT EDİLDİ!</strong>
        </div>
        <div style="font-size: 0.9rem;">
            <strong>${transaction.coin}</strong> - ${transaction.type === 'buy' ? 'ALIŞ' : 'SATIŞ'}<br>
            <strong>$${formatNumber(transaction.usdValue)}</strong><br>
            <small>${formatTime(transaction.timestamp)}</small>
        </div>
    `;

    document.body.appendChild(alertDiv);

    // 8 saniye sonra kaldır
    setTimeout(() => {
        alertDiv.style.animation = 'slideOut 0.5s ease-in';
        setTimeout(() => alertDiv.remove(), 500);
    }, 8000);
}

// CSS animasyonları ekle
if (!document.getElementById('whaleAlertStyles')) {
    const style = document.createElement('style');
    style.id = 'whaleAlertStyles';
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
}

// İşlem hash'i oluştur
function generateTxHash() {
    const chars = '0123456789abcdef';
    let hash = '0x';
    for (let i = 0; i < 64; i++) {
        hash += chars[Math.floor(Math.random() * chars.length)];
    }
    return hash;
}

// İstatistikleri güncelle
function updateStatistics() {
    const now = Date.now();
    const last24h = now - 86400000;

    const recent24h = whaleTransactions.filter(tx => tx.timestamp > last24h);
    const totalWhales = recent24h.length;
    const totalVolume = recent24h.reduce((sum, tx) => sum + tx.usdValue, 0);
    const largestTx = Math.max(...recent24h.map(tx => tx.usdValue));

    document.getElementById('totalWhales').textContent = totalWhales.toLocaleString();
    document.getElementById('totalVolume').textContent = '$' + formatNumber(totalVolume);
    document.getElementById('largestTx').textContent = '$' + formatNumber(largestTx);
    document.getElementById('last24h').textContent = totalWhales.toLocaleString();
}

// Sayı formatlama
function formatNumber(num) {
    if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
    if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
    if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
    return num.toFixed(2);
}

// Grafik oluştur
function createChart() {
    const ctx = document.getElementById('whaleChart').getContext('2d');

    // Son 24 saatlik veriyi saatlik gruplara böl
    const hourlyData = getHourlyData();

    chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: hourlyData.labels,
            datasets: [{
                label: 'Balina İşlem Hacmi (USD)',
                data: hourlyData.values,
                borderColor: '#00d4ff',
                backgroundColor: 'rgba(0, 212, 255, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    labels: {
                        color: '#ffffff'
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: '#cccccc'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                y: {
                    ticks: {
                        color: '#cccccc',
                        callback: function(value) {
                            return '$' + formatNumber(value);
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            }
        }
    });
}

// Saatlik veri hazırla
function getHourlyData() {
    const now = Date.now();
    const hours = [];
    const values = [];

    for (let i = 23; i >= 0; i--) {
        const hourStart = now - (i * 3600000);
        const hourEnd = hourStart + 3600000;

        const hourTxs = whaleTransactions.filter(tx =>
            tx.timestamp >= hourStart && tx.timestamp < hourEnd
        );

        const hourVolume = hourTxs.reduce((sum, tx) => sum + tx.usdValue, 0);

        const date = new Date(hourStart);
        hours.push(date.getHours() + ':00');
        values.push(hourVolume);
    }

    return { labels: hours, values: values };
}

// Grafik güncelle
function updateChart() {
    if (chart) {
        const hourlyData = getHourlyData();
        chart.data.labels = hourlyData.labels;
        chart.data.datasets[0].data = hourlyData.values;
        chart.update('none');
    }
}

// İşlem tablosunu güncelle
function updateTransactionTable() {
    const tbody = document.getElementById('whaleTableBody');
    const filteredTxs = getFilteredTransactions();

    tbody.innerHTML = '';

    filteredTxs.slice(0, 20).forEach(tx => {
        // Güvenli veri erişimi
        const coinInfo = tx.coinId ? cryptoData[tx.coinId] : null;
        const coinName = coinInfo ? coinInfo.name : 'Bilinmeyen';
        const coinSymbol = tx.coin || 'N/A';
        const displaySymbol = coinInfo ? coinInfo.displaySymbol : coinSymbol;

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${formatTime(tx.timestamp)}</td>
            <td>
                <strong>${coinSymbol}</strong><br>
                <small>${coinName}</small>
            </td>
            <td>${formatNumber(tx.amount)} ${displaySymbol}</td>
            <td>$${formatNumber(tx.usdValue)}</td>
            <td>
                <code style="font-size: 0.8rem;">
                    ${tx.hash.substring(0, 10)}...${tx.hash.substring(tx.hash.length - 8)}
                </code>
            </td>
            <td>
                <span class="transaction-type ${tx.type}">
                    ${tx.type === 'buy' ? 'ALIŞ' : 'SATIŞ'}
                </span>
            </td>
            <td>
                <span class="transaction-status ${tx.status}">
                    ${tx.status === 'confirmed' ? 'Onaylandı' : 'Beklemede'}
                </span>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Filtrelenmiş işlemleri getir
function getFilteredTransactions() {
    const coinFilter = document.getElementById('coinFilter').value;
    const minAmount = parseFloat(document.getElementById('minAmount').value) || 0;
    const timeFilter = document.getElementById('timeFilter').value;

    let filtered = whaleTransactions;

    // Coin filtresi (symbol ile karşılaştır)
    if (coinFilter !== 'all') {
        filtered = filtered.filter(tx => tx.coin === coinFilter);
    }

    // Minimum tutar filtresi
    if (minAmount > 0) {
        filtered = filtered.filter(tx => tx.usdValue >= minAmount);
    }

    // Zaman filtresi
    const now = Date.now();
    let timeLimit;
    switch (timeFilter) {
        case '1h':
            timeLimit = now - 3600000;
            break;
        case '24h':
            timeLimit = now - 86400000;
            break;
        case '7d':
            timeLimit = now - 604800000;
            break;
        case '30d':
            timeLimit = now - 2592000000;
            break;
        default:
            timeLimit = 0;
    }

    filtered = filtered.filter(tx => tx.timestamp >= timeLimit);

    return filtered;
}

// Zaman formatlama
function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) return 'Az önce';
    if (diff < 3600000) return Math.floor(diff / 60000) + ' dk önce';
    if (diff < 86400000) return Math.floor(diff / 3600000) + ' sa önce';

    return date.toLocaleDateString('tr-TR') + ' ' + date.toLocaleTimeString('tr-TR', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Uyarıları güncelle
function updateAlerts() {
    const container = document.getElementById('alertsContainer');
    const recentLarge = whaleTransactions
        .filter(tx => tx.timestamp > Date.now() - 3600000 && tx.usdValue > 5000000)
        .slice(0, 5);

    container.innerHTML = '';

    recentLarge.forEach(tx => {
        const alert = document.createElement('div');
        const severity = tx.usdValue > 10000000 ? 'high' : 'medium';

        // Güvenli veri erişimi
        const coinInfo = tx.coinId ? cryptoData[tx.coinId] : null;
        const coinSymbol = tx.coin || 'N/A';
        const displaySymbol = coinInfo ? coinInfo.displaySymbol : coinSymbol;

        alert.className = `alert ${severity}`;
        alert.innerHTML = `
            <div class="alert-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="alert-content">
                <strong>Büyük ${tx.type === 'buy' ? 'Alış' : 'Satış'} İşlemi Tespit Edildi!</strong><br>
                ${formatNumber(tx.amount)} ${displaySymbol} (${coinSymbol}) -
                $${formatNumber(tx.usdValue)} - ${formatTime(tx.timestamp)}
            </div>
        `;

        container.appendChild(alert);
    });

    if (recentLarge.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #cccccc;">Son 1 saatte büyük işlem tespit edilmedi.</p>';
    }
}

// Filtreleri uygula
function applyFilters() {
    updateTransactionTable();
}

// Sayfa kapatılırken interval'ları temizle
window.addEventListener('beforeunload', function() {
    if (updateInterval) {
        clearInterval(updateInterval);
    }
    if (priceUpdateInterval) {
        clearInterval(priceUpdateInterval);
    }
});

// Gerçek zamanlı durum göstergesi güncelle
function updateConnectionStatus() {
    const statusDot = document.querySelector('.status-dot');
    const statusText = statusDot?.parentElement;

    if (statusDot && statusText) {
        // API bağlantısını kontrol et
        const hasRecentData = Object.values(cryptoData).some(coin => coin.price > 0);

        if (hasRecentData) {
            statusDot.style.background = '#00ff00';
            statusText.innerHTML = '<span class="status-dot"></span>Canlı - Gerçek Veriler';
            statusText.style.color = '#00ff00';
        } else {
            statusDot.style.background = '#ffa500';
            statusText.innerHTML = '<span class="status-dot"></span>Bağlantı Kuruluyor...';
            statusText.style.color = '#ffa500';
        }
    }
}

// Sayfa görünürlük değişikliklerini izle
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // Sayfa gizlendiğinde güncelleme sıklığını azalt
        if (updateInterval) {
            clearInterval(updateInterval);
            updateInterval = setInterval(() => {
                generateNewTransaction();
                updateStatistics();
                updateChart();
                updateTransactionTable();
                updateAlerts();
            }, 30000); // 30 saniyede bir
        }
    } else {
        // Sayfa tekrar görünür olduğunda normal sıklığa dön
        if (updateInterval) {
            clearInterval(updateInterval);
            updateInterval = setInterval(() => {
                generateNewTransaction();
                updateStatistics();
                updateChart();
                updateTransactionTable();
                updateAlerts();
            }, 10000); // 10 saniyede bir
        }

        // Hemen bir güncelleme yap
        generateNewTransaction();
        updateStatistics();
        updateChart();
        updateTransactionTable();
        updateAlerts();
    }
});

// Bağlantı durumunu periyodik olarak kontrol et
setInterval(updateConnectionStatus, 5000);
